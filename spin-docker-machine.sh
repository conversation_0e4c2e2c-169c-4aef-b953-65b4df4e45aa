#!/usr/bin/env bash

docker rm keycloak-testing-container || true

cd ./react-theme/build_keycloak

docker run \
   -p 8080:8080 \
   --name keycloak-testing-container \
   -e KEYCLOAK_ADMIN=admin \
   -e KEYCLOAK_ADMIN_PASSWORD=admin \
   -e JAVA_OPTS=-Dkeycloak.profile=preview \
   -v ./react-theme/build_keycloak/src/main/resources/theme/keycloakify-starter:/opt/keycloak/themes/keycloakify-starter:rw \
   -it quay.io/keycloak/keycloak:17.0 \
   start-dev
