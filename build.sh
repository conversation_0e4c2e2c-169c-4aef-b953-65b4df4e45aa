docker login dxb.ocir.io -u axjdg8m7rpbd/oracleidentitycloudservice/ajadaa  -p e0nrLAqy8SP6o8h{i_29
COMMIT_HASH=$(git rev-parse --short HEAD)
docker build --build-arg REACT_APP_API_URL=https://api.sk-dev.sehacity.com/graphql -t  dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.dev-$COMMIT_HASH .
docker build --build-arg REACT_APP_API_URL=https://api.sk-test.sehacity.com/gateway/graphql -t  dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.test-$COMMIT_HASH .
docker build --build-arg REACT_APP_API_URL=https://api.stg.iohealth.com/gateway/graphql -t  dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.iohealth-stg-$COMMIT_HASH .
docker build --build-arg REACT_APP_API_URL=https://api.sk-demo.sehacity.com/gateway/graphql -t  dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.sk-demo-$COMMIT_HASH .

docker push dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.dev-$COMMIT_HASH
docker push dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.test-$COMMIT_HASH
docker push dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.iohealth-stg-$COMMIT_HASH
docker push dxb.ocir.io/axjdg8m7rpbd/keycloak:0.5.sk-demo-$COMMIT_HASH