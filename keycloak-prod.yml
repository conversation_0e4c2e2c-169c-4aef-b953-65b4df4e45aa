version: '2.3'
services:
  keycloak:
    image: dxb.ocir.io/axjdg8m7rpbd/keycloak:0.2.2
    command:
      - start --auto-build --spi-login-protocol-openid-connect-legacy-logout-redirect-uri=true --features="admin-fine-grained-authz,token-exchange"
    environment:
      - KC_PROXY=edge
      - KC_HOSTNAME_STRICT=false
      - KC_HTTP_PORT=/auth
      - KC_HTTP_PORT=9080
      - KC_HTTP_ENABLED=true
      - KC_HOSTNAME_STRICT_HTTPS=false
      - KC_HTTP_RELATIVE_PATH=/auth
      - KEYCLOAK_ADMIN=master_admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
      - KC_DB=mysql
      - KC_DB_URL_HOST=gis_db
      - KC_DB_URL_DATABASE=keycloak1
      - KC_DB_USERNAME=root
      - KC_DB_PASSWORD=root
      - PROXY_ADDRESS_FORWARDING=true
      - ADMIN_USER=<EMAIL>
      - HOSTNAME_STRICT=false
      - KC_CACHE_CONFIG_FILE=jdbc-ping.xml
      - WORK_FLOW_URL=http://accurtonflow:8080
      - GATEWAY=https://api.sk-dev.sehacity.com/graphql/
    ports:
      - 9080:9080
      - 8080:8080
      - 9443:9443
      - 10990:10990
    networks:
      - sehacity-backend-tier
    restart: unless-stopped

networks:
  sehacity-backend-tier:
    driver: bridge
    name: sehacity-backend-tier