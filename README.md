# chinaing theme with react

```
    cd react-theme
    yarn
    yarn start
```

then navigate to react-theme/src/keycloak-theme/kcContext.ts
and uncomment

```
    // mockPageId: "login.ftl",
```

now you can style the login page ...

to build your app and test real with keycloak you

1. yarn build-keycloak-theme
   this command will generate a build jar file, that we wil lcopy next to either the docker machine or to the standalone docker
   now you have two options
1. either you run new docker container v 17+  
   ` ./spin-docker-machine.sh`

or you download keycloak.v21.1.0
then copy the generated gar file ( usually found in this path react-theme/build_keycloak/target/{arifact-id-name-version.jar})

where arifact-id-name is mentioned or set in package json
and go to /path/to/keycloak.v21.1.0/standalone/deployments

now run ./path/to/keycloak.v21.1.0/bin/standalone.sh

or simple shortcut run
` ./standalone.sh`
