version: '2.3'
services:
  keycloak:
    build: .
    command:
      - start
      - --features=admin-fine-grained-authz,token-exchange
      - --spi-oauth2-token-exchange-default-enabled=false
      - --spi-login-protocol-openid-connect-legacy-logout-redirect-uri=true
    environment:
      - KC_PROXY=edge
      - KC_HOSTNAME_STRICT=false
      - KC_HTTP_ENABLED=true
      - KC_HOSTNAME_STRICT_HTTPS=false
      - KC_HTTP_RELATIVE_PATH=/auth
      - PROXY_ADDRESS_FORWARDING=true
      - HOSTNAME_STRICT=false
      - KC_DB=mysql
      - KC_CACHE_CONFIG_FILE=mysql-jdbc-ping.xml
      - KC_DB_URL_HOST=localhost
      - KC_DB_URL_DATABASE=test_keycloak
      - ADMIN_USER=service-account-integration_client
      - GATEWAY=http://gateway/graphql
      - WORK_FLOW_URL=http://accurtonflow:8080
      - REACT_APP_API_URL:http://api.sk-dev2.sehacity.com/graphql
      - CUSTOMER_REGISTRATION_FEATURE_ENABLED=true
      - KEYCLOAK_ADMIN=master_admin
      - KEYCLOAK_ADMIN_PASSWORD=admin
      - KC_DB_USERNAME=root
      - KC_DB_PASSWORD=Test@12345
    ports:
      - 9080:9080
      - 9443:9443
      - 10990:10990
      - 8787:8787
    networks:
      - sehacity-backend-tier
    restart: unless-stopped

networks:
  sehacity-backend-tier:
    driver: bridge
    name: sehacity-backend-tier