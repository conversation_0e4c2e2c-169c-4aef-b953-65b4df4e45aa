FROM node:16-alpine as theme
WORKDIR /app
COPY react-theme/package.json .
COPY react-theme/yarn.lock .
RUN yarn install
COPY react-theme .
RUN yarn build-keycloak-theme

FROM maven:3.8.4-openjdk-17 as plugin

RUN mkdir -p /home/<USER>/.m2/repository

COPY pom.xml settings.xml /home/<USER>/
RUN mvn --batch-mode -s /home/<USER>/settings.xml -f /home/<USER>/pom.xml dependency:resolve

COPY src /home/<USER>/src
RUN mvn --batch-mode -s /home/<USER>/settings.xml  -f /home/<USER>/pom.xml clean package

COPY mysql-jdbc-ping.xml /home/<USER>/mysql-jdbc-ping.xml
COPY mssql-jdbc-ping.xml /home/<USER>/mssql-jdbc-ping.xml
COPY apple-identity-provider-1.12.0.jar /home/<USER>/apple-identity-provider-1.12.0.jar


FROM quay.io/keycloak/keycloak:23.0.7 as builder
ENV KC_HEALTH_ENABLED=true
ENV KC_METRICS_ENABLED=true
ENV KC_HTTP_RELATIVE_PATH=/auth
ENV KC_DB=mysql
ENV KC_CACHE_CONFIG_FILE=mysql-jdbc-ping.xml
COPY --from=plugin /home/<USER>/mysql-jdbc-ping.xml /opt/keycloak/conf/mysql-jdbc-ping.xml
COPY --from=plugin /home/<USER>/mssql-jdbc-ping.xml /opt/keycloak/conf/mssql-jdbc-ping.xml
COPY --from=plugin /home/<USER>/target/uae_pass-jar-with-dependencies.jar /opt/keycloak/providers/plugin.jar
COPY --from=theme /app/build_keycloak/target/*.jar /opt/keycloak/providers/theme.jar
COPY --from=plugin /home/<USER>/apple-identity-provider-1.12.0.jar /opt/keycloak/providers/apple-identity-provider-1.12.0.jar
WORKDIR /opt/keycloak
RUN /opt/keycloak/bin/kc.sh build --features="admin-fine-grained-authz,token-exchange"

FROM quay.io/keycloak/keycloak:23.0.7
COPY --from=builder /opt/keycloak/ /opt/keycloak/
ENV KC_PROXY=passthrough
ENV KC_HOSTNAME_STRICT=false
ENV KC_HTTP_PORT=9080
ENV KC_HTTP_ENABLED=true
ENV KC_HOSTNAME_STRICT_HTTPS=false
ENV KC_HTTP_RELATIVE_PATH=/auth
ENTRYPOINT ["/opt/keycloak/bin/kc.sh"]