{"name": "<PERSON><PERSON><PERSON>-theme", "homepage": "https://starter.keycloakify.dev", "version": "3.1.0", "description": "A starter/demo project for keycloakify", "repository": {"type": "git", "url": "git://github.com/codegouvfr/keycloakify-starter.git"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "build-keycloak-theme": "yarn build && keycloakify", "download-builtin-keycloak-theme": "download-builtin-keycloak-theme 15.0.2"}, "keycloakify": {"extraThemeProperties": ["REACT_APP_API_URL=${env.REACT_APP_API_URL:}"], "artifactId": "uae_pass", "name": "<PERSON><PERSON><PERSON>", "extraPages": ["my-extra-page-1.ftl", "my-extra-page-2.ftl", "otp.ftl"]}, "author": "u/garronej", "license": "MIT", "keywords": [], "dependencies": {"@apollo/client": "^3.8.3", "@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/material": "^5.14.7", "evt": "^2.4.15", "graphql": "^16.8.1", "jwt-decode": "^3.1.2", "keycloak-js": "^21.0.1", "keycloakify": "^6.12.7", "libphonenumber-js": "^1.10.44", "powerhooks": "^0.26.2", "react": "18.1.0", "react-dom": "18.1.0", "strip-ansi": "^7.1.0", "tsafe": "^1.4.3"}, "devDependencies": {"@types/node": "^15.3.1", "@types/react": "18.0.9", "@types/react-dom": "18.0.4", "react-scripts": "5.0.1", "typescript": "~4.8.0"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"], "rules": {"react-hooks/exhaustive-deps": "off", "@typescript-eslint/no-redeclare": "off", "no-labels": "off"}}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}