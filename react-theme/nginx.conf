server {
    listen 80;
    
    gzip on; 
    gzip_vary on; 
    gzip_min_length 1024; 
    gzip_proxied expired no-cache no-store private auth; 
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/javascript application/xml; 
    gzip_disable "MSIE [1-6]\.";

    root /usr/share/nginx/html;
    index index.html;      

    try_files $uri $uri/ /index.html;

    # Any route containing a file extension (e.g. /devicesfile.js)
    location ~ ^.+\..+$ {
      try_files $uri =404;

      location ~* \.(?:html|json|txt)$ {
        expires -1;
      }

      # CRA generates filenames with hashes so we can
      # tell the browser to keep in cache the resources.
      location ~* \.(?:css|js|md|woff2?|eot|ttf|xml)$ {
        expires 1y;
        access_log off;
        add_header Cache-Control "public";

        # Allow fonts and scripts to be downloaded from pages
        # served by Keycloak (CORS for --external-request)
        # We allow .md for beeing able to fetch the terms of service.
        location ~* \.(?:js|woff2?|eot|ttf|xml|md)$ {
          add_header Access-Control-Allow-Origin *;
        }

      }

    }

}
