<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />


  <title>ioHealth</title>
  <link rel="icon" href="%PUBLIC_URL%/Group-11174.ico"Group-11174.ico/>
  <style>
    /* src/fonts.css */
    @font-face {
      font-family: 'book';
      font-weight: normal;
      font-style: normal;
      src: url("%PUBLIC_URL%/fonts/AirbnbCerealBook.woff") format("woff");
    }

    @font-face {
      font-family: 'bold';
      font-weight: normal;
      font-style: normal;
      src: url("%PUBLIC_URL%/fonts/AirbnbCerealBold.woff") format("woff");
    }

    @font-face {
      font-family: 'medium';
      font-weight: normal;
      font-style: normal;
      src: url("%PUBLIC_URL%/fonts/AirbnbCerealMedium.woff") format("woff");
    }

    @font-face {
      font-family: 'black';
      font-weight: normal;
      font-style: normal;
      src: url("%PUBLIC_URL%/fonts/AirbnbCerealBlack.woff") format("woff");
    }

    :root {
      --white: #fff;
      --nav_bar_width: 55px;
      --main-color: #25287F;
      --black:#000;
      --deep-purple: #913263;
      --background-1:#F8F9FB;
      --error-color: #B93535;
      --error-background:#FFF7F7;
      --main-Book: 'book';
      --main-Bold: 'bold';
      --main-Medium: 'medium';
      --main-Black: 'black';
      --toast-warning:#D46A3D;
      --toast-danger:#B93535;
      --toast-secuess:#46A200;
      --toast-info:#252880;
    }
    
  </style>

</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
</body>

</html>