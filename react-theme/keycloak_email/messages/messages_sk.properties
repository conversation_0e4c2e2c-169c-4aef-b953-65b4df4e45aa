# encoding: utf-8
emailVerificationSubject=Overenie e-mailu
emailVerificationBody=Niekto vytvoril ÃºÄet {2} s touto e-mailovou adresou. Ak ste to vy, kliknite na niÅ¾Å¡ie uvedenÃ½ odkaz a overte svoju e-mailovÃº adresu \n\n{0}\n\nTento odkaz uplynie do {1} minÃºt.\n\nAk ste tento ÃºÄet nevytvorili, ignorujte tÃºto sprÃ¡vu.
emailVerificationBodyHtml=<p>Niekto vytvoril ÃºÄet {2} s touto e-mailovou adresou. Ak ste to vy, kliknite na niÅ¾Å¡ie uvedenÃ½ odkaz na overenie svojej e-mailovej adresy.</p><p><a href="{0}"> Odkaz na overenie e-mailovej adresy </a></p><p>PlatnosÅ¥ odkazu vyprÅ¡Ã­ za {1} minÃºt.</p><p> Ak ste tento ÃºÄet nevytvorili, ignorujte tÃºto sprÃ¡vu.</p>
emailTestSubject=[KEYCLOAK] - Testovacia sprÃ¡va SMTP
emailTestBody=Toto je skÃºÅ¡obnÃ¡ sprÃ¡va
emailTestBodyHtml=<p>Toto je skÃºÅ¡obnÃ¡ sprÃ¡va</p>
identityProviderLinkSubject=Odkaz {0}
identityProviderLinkBody=Niekto chce prepojiÅ¥ vÃ¡Å¡ ÃºÄet "{1}" s ÃºÄtom {0}"pouÅ¾Ã­vateÄ¾a {2}. Ak ste to vy, kliknutÃ­m na odkaz niÅ¾Å¡ie prepojte ÃºÄty. \n\n{3}\n\nTento odkaz uplynie do {4} minÃºt.\n\nAk nechcete prepojiÅ¥ ÃºÄet, jednoducho ignorujte tÃºto sprÃ¡vu , Ak prepÃ¡jate ÃºÄty, budete sa mÃ´cÅ¥ prihlÃ¡siÅ¥ do {1} aÅ¾ {0}.
identityProviderLinkBodyHtml=<p>Niekto chce prepojiÅ¥ vÃ¡Å¡ ÃºÄet <b>{1}</b> s ÃºÄtom <b>{0}</b> pouÅ¾Ã­vateÄ¾a {2}. Ak ste to vy, kliknutÃ­m na odkaz niÅ¾Å¡ie prepojte ÃºÄty</p><p><a href="{3}">Odkaz na potvrdenie prepojenia ÃºÄtu </a></p><p> PlatnosÅ¥ tohto odkazu vyprÅ¡Ã­ v rÃ¡mci {4} minÃºt.</p><p>Ak nechcete prepojiÅ¥ ÃºÄet, ignorujte tÃºto sprÃ¡vu. Ak prepojujete ÃºÄty, budete sa mÃ´cÅ¥ prihlÃ¡siÅ¥ do {1} aÅ¾ {0}.</p>
passwordResetSubject=Obnovenie hesla
passwordResetBody=Niekto poÅ¾iadal, aby ste zmenili svoje poverenia ÃºÄtu {2}. Ak ste to vy, kliknite na odkaz uvedenÃ½ niÅ¾Å¡ie, aby ste ich vynulovali.\n\n{0}\n\nTento odkaz a kÃ³d uplynie do {1} minÃºt.\n\nAk nechcete obnoviÅ¥ svoje poverenia , ignorujte tÃºto sprÃ¡vu a niÄ sa nezmenÃ­.
passwordResetBodyHtml=<p>Niekto poÅ¾iadal, aby ste zmenili svoje poverenia ÃºÄtu {2}. Ak ste to vy, kliknutÃ­m na odkaz niÅ¾Å¡ie ich resetujte.</p><p><a href="{0}">Odkaz na obnovenie poverenÃ­ </a></p><p>PlatnosÅ¥ tohto odkazu vyprÅ¡Ã­ v priebehu {1} minÃºt.</p><p>Ak nechcete obnoviÅ¥ svoje poverenia, ignorujte tÃºto sprÃ¡vu a niÄ sa nezmenÃ­.</p>
executeActionsSubject=Aktualizujte svoj ÃºÄet
executeActionsBody=VÃ¡Å¡ administrÃ¡tor prÃ¡ve poÅ¾iadal o aktualizÃ¡ciu vÃ¡Å¡ho ÃºÄtu {2} vykonanÃ­m nasledujÃºcich akciÃ­: {3}. KliknutÃ­m na odkaz uvedenÃ½ niÅ¾Å¡ie spustÃ­te tento proces.\n\n{0}\n\nTento odkaz vyprÅ¡Ã­ za {1} minÃºty.\n\nAk si nie ste vedomÃ½, Å¾e vÃ¡Å¡ adminstrÃ¡tor o toto poÅ¾iadal, ignorujte tÃºto sprÃ¡vu a niÄ bude zmenenÃ©.
executeActionsBodyHtml=<p>VÃ¡Å¡ sprÃ¡vca prÃ¡ve poÅ¾iadal o aktualizÃ¡ciu vÃ¡Å¡ho ÃºÄtu {2} vykonanÃ­m nasledujÃºcich akciÃ­: {3}. KliknutÃ­m na odkaz uvedenÃ½ niÅ¾Å¡ie spustÃ­te tento proces.</p><p><a href="{0}"> Odkaz na aktualizÃ¡ciu ÃºÄtu </a></p><p> PlatnosÅ¥ tohto odkazu uplynie do {1} minÃºty.</p><p> Ak si nie ste vedomÃ­, Å¾e vÃ¡Å¡ adminstrÃ¡tor o toto poÅ¾iadal, ignorujte tÃºto sprÃ¡vu a niÄ sa nezmenÃ­.</p>
eventLoginErrorSubject=Chyba prihlÃ¡senia
eventLoginErrorBody=Bol zistenÃ½ neÃºspeÅ¡nÃ½ pokus o prihlÃ¡senie do vÃ¡Å¡ho ÃºÄtu v {0} z {1}. Ak ste to neboli vy, obrÃ¡Å¥te sa na administrÃ¡tora.
eventLoginErrorBodyHtml=<p>Bol zistenÃ½ neÃºspeÅ¡nÃ½ pokus o prihlÃ¡senie vÃ¡Å¡ho ÃºÄtu na {0} z {1}. Ak ste to neboli vy, kontaktujte administrÃ¡tora.</p>
eventRemoveTotpSubject=OdstrÃ¡niÅ¥ TOTP
eventRemoveTotpBody=OTP bol odstrÃ¡nenÃ½ z vÃ¡Å¡ho ÃºÄtu dÅa {0} z {1}. Ak ste to neboli vy, obrÃ¡Å¥te sa na administrÃ¡tora.
eventRemoveTotpBodyHtml=<p>OTP bol odstrÃ¡nenÃ½ z vÃ¡Å¡ho ÃºÄtu dÅa {0} z {1}. Ak ste to neboli vy, kontaktujte administrÃ¡tora.</p>
eventUpdatePasswordSubject=AktualizovaÅ¥ heslo
eventUpdatePasswordBody=VaÅ¡e heslo bolo zmenenÃ© na {0} z {1}. Ak ste to neboli vy, obrÃ¡Å¥te sa na administrÃ¡tora.
eventUpdatePasswordBodyHtml=<p>VaÅ¡e heslo bolo zmenenÃ© na {0} z {1}. Ak ste to neboli vy, kontaktujte administrÃ¡tora.</p>
eventUpdateTotpSubject=AktualizÃ¡cia TOTP
eventUpdateTotpBody=TOTP bol aktualizovanÃ½ pre vÃ¡Å¡ ÃºÄet na {0} z {1}. Ak ste to neboli vy, obrÃ¡Å¥te sa na administrÃ¡tora.
eventUpdateTotpBodyHtml=<p>TOTP bol aktualizovanÃ½ pre vÃ¡Å¡ ÃºÄet dÅa {0} z {1}. Ak ste to neboli vy, kontaktujte administrÃ¡tora.</p>

requiredAction.CONFIGURE_TOTP=KonfigurÃ¡cia OTP
requiredAction.terms_and_conditions=ZmluvnÃ© podmienky
requiredAction.UPDATE_PASSWORD=AktualizovaÅ¥ heslo
requiredAction.UPDATE_PROFILE=AktualizovaÅ¥ profil
requiredAction.VERIFY_EMAIL=OveriÅ¥ e-mail

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=sekundy
linkExpirationFormatter.timePeriodUnit.seconds.1=sekunda
linkExpirationFormatter.timePeriodUnit.minutes=minuty
linkExpirationFormatter.timePeriodUnit.minutes.1=minÃºta
#for language which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like this:
#linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.4=minutu
linkExpirationFormatter.timePeriodUnit.hours=hodiny
linkExpirationFormatter.timePeriodUnit.hours.1=hodina
linkExpirationFormatter.timePeriodUnit.days=dni
linkExpirationFormatter.timePeriodUnit.days.1=deÅ 
