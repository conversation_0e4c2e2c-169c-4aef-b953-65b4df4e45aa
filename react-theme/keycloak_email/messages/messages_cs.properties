# encoding: utf-8
emailVerificationSubject=OvÄÅenÃ­ e-mailu
emailVerificationBody=NÄkdo vytvoÅil ÃºÄet {2} s touto e-mailovou adresou. Pokud jste to vy, kliknÄte na nÃ­Å¾e uvedenÃ½ odkaz a ovÄÅte svou e-mailovou adresu \n\n{0}\n\nTento odkaz vyprÅ¡Ã­ za {3}.\n\nPokud jste tento ÃºÄet nevytvoÅili, tuto zprÃ¡vu ignorujte.
emailVerificationBodyHtml=<p>NÄkdo vytvoÅil ÃºÄet {2} s touto e-mailovou adresou. Pokud jste to vy, kliknÄte na nÃ­Å¾e uvedenÃ½ odkaz a ovÄÅte svou e-mailovou adresu. </p><p><a href="{0}">Odkaz na ovÄÅenÃ­ e-mailovÃ© adresy</a></p><p>Platnost odkazu vyprÅ¡Ã­ za {3}.</p><p>Pokud jste tento ÃºÄet nevytvoÅili, tuto zprÃ¡vu ignorujte.</p>
emailTestSubject=[KEYCLOAK] - testovacÃ­ zprÃ¡va
emailTestBody=Toto je testovacÃ­ zprÃ¡va
emailTestBodyHtml=<p>Toto je testovacÃ­ zprÃ¡va </p>
identityProviderLinkSubject=Odkaz {0}
identityProviderLinkBody=NÄkdo chce propojit vÃ¡Å¡ ÃºÄet "{1}" s ÃºÄtem "{0}" uÅ¾ivatele {2}. Pokud jste to vy, kliknÄte na nÃ­Å¾e uvedenÃ½ odkaz a propojte ÃºÄty. \n\n{3}\n\nPlatnost tohoto odkazu je {5}.\n\nPokud nechcete propojit ÃºÄet, tuto zprÃ¡vu ignorujte. Pokud propojÃ­te ÃºÄty, budete se moci pÅihlÃ¡sit jako {1} pomocÃ­ {0}.
identityProviderLinkBodyHtml=<p>NÄkdo chce propojit vÃ¡Å¡ ÃºÄet <b>{1}</b> s ÃºÄtem <b>{0}</b> uÅ¾ivatele {2}. Pokud jste to vy, kliknÄte na nÃ­Å¾e uvedenÃ½ odkaz a propojte ÃºÄty.</p><p><a href="{3}">Odkaz na propojenÃ­ ÃºÄtÅ¯.</a></p><p> Platnost tohoto odkazu je {5}. </p><p> Pokud nechcete propojit ÃºÄet, tuto zprÃ¡vu ignorujte. Pokud propojÃ­te ÃºÄty, budete se moci pÅihlÃ¡sit jako {1} pomocÃ­ {0}.</p>
passwordResetSubject=ZapomenutÃ© heslo
passwordResetBody=NÄkdo prÃ¡vÄ poÅ¾Ã¡dal o zmÄnu hesla u vaÅ¡eho ÃºÄtu {2}. Pokud jste to vy, pro jeho zmÄnu kliknÄte na odkaz nÃ­Å¾e.\n\n{0}\n\nPlatnost tohoto odkazu je {3}.\n\nPokud heslo zmÄnit nechcete, tuto zprÃ¡vu ignorujte a nic se nezmÄnÃ­.
passwordResetBodyHtml=<p>NÄkdo prÃ¡vÄ poÅ¾Ã¡dal o zmÄnu povÄÅenÃ­ vaÅ¡eho ÃºÄtu {2}. Pokud jste to vy, kliknÄte na odkaz nÃ­Å¾e, abyste je resetovali.</p><p><a href="{0}">Odkaz na obnovenÃ­ povÄÅenÃ­ </a></p><p> Platnost tohoto odkazu vyprÅ¡Ã­ bÄhem {3}.</p><p> Pokud nechcete obnovit vaÅ¡e povÄÅenÃ­, ignorujte tuto zprÃ¡vu a nic se nezmÄnÃ­.</p>
executeActionsSubject=Aktualizujte svÅ¯j ÃºÄet
executeActionsBody=VÃ¡Å¡ administrÃ¡tor vÃ¡s poÅ¾Ã¡dal o provedenÃ­ nÃ¡sledujÃ­cÃ­ch akcÃ­ u ÃºÄtu {2}: {3}. ZaÄnÄte kliknutÃ­m na nÃ­Å¾e uvedenÃ½ odkaz.\n\n{0}\n\nPlatnost tohoto odkazu je {4}.\n\nPokud si nejste jisti, zda je tento poÅ¾adavek v poÅÃ¡dku, ignorujte tuto zprÃ¡vu.
executeActionsBodyHtml=<p>VÃ¡Å¡ administrÃ¡tor vÃ¡s poÅ¾Ã¡dal o provedenÃ­ nÃ¡sledujÃ­cÃ­ch akcÃ­ u ÃºÄtu {2}: {3}. ZaÄnÄte kliknutÃ­m na nÃ­Å¾e uvedenÃ½ odkaz.</p><p><a href="{0}">Odkaz na aktualizaci ÃºÄtu.</a></p><p>Platnost tohoto odkazu je {4}.</p><p>Pokud si nejste jisti, zda je tento poÅ¾adavek v poÅÃ¡dku, ignorujte tuto zprÃ¡vu.</p>
eventLoginErrorSubject=Chyba pÅihlÃ¡Å¡enÃ­
eventLoginErrorBody=NÄkdo se neÃºspÄÅ¡nÄ pokusil pÅihlÃ¡sit k ÃºÄtu {0} z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.
eventLoginErrorBodyHtml=<p>NÄkdo se neÃºspÄÅ¡nÄ pokusil pÅihlÃ¡sit k ÃºÄtu {0} z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.</p>
eventRemoveTotpSubject=Odebrat TOTP
eventRemoveTotpBody=V ÃºÄtu {0} bylo odebrÃ¡no nastavenÃ­ OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.
eventRemoveTotpBodyHtml=<p>V ÃºÄtu {0} bylo odebrÃ¡no nastavenÃ­ OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.</p>
eventUpdatePasswordSubject=Aktualizace hesla
eventUpdatePasswordBody=V ÃºÄtu {0} bylo zmÄnÄno heslo z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.
eventUpdatePasswordBodyHtml=<p>V ÃºÄtu {0} bylo zmÄnÄno heslo z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.</p>
eventUpdateTotpSubject=Aktualizace OTP
eventUpdateTotpBody=V ÃºÄtu {0} bylo zmÄnÄno nastavenÃ­ OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.
eventUpdateTotpBodyHtml=<p>V ÃºÄtu {0} bylo zmÄnÄno nastavenÃ­ OTP z {1}. Pokud jste to nebyli vy, kontaktujte administrÃ¡tora.</p>

requiredAction.CONFIGURE_TOTP=Konfigurace OTP
requiredAction.terms_and_conditions=SmluvnÃ­ podmÃ­nky
requiredAction.UPDATE_PASSWORD=Aktualizace hesla
requiredAction.UPDATE_PROFILE=Aktualizace profilu
requiredAction.VERIFY_EMAIL=OvÄÅenÃ­ e-mailu

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=sekund
linkExpirationFormatter.timePeriodUnit.seconds.1=sekunda
linkExpirationFormatter.timePeriodUnit.seconds.2=sekundy
linkExpirationFormatter.timePeriodUnit.seconds.3=sekundy
linkExpirationFormatter.timePeriodUnit.seconds.4=sekundy
linkExpirationFormatter.timePeriodUnit.minutes=minut
linkExpirationFormatter.timePeriodUnit.minutes.1=minuta
linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=hodin
linkExpirationFormatter.timePeriodUnit.hours.1=hodina
linkExpirationFormatter.timePeriodUnit.hours.2=hodiny
linkExpirationFormatter.timePeriodUnit.hours.3=hodiny
linkExpirationFormatter.timePeriodUnit.hours.4=hodiny
linkExpirationFormatter.timePeriodUnit.days=dnÃ­
linkExpirationFormatter.timePeriodUnit.days.1=den
linkExpirationFormatter.timePeriodUnit.days.2=dny
linkExpirationFormatter.timePeriodUnit.days.3=dny
linkExpirationFormatter.timePeriodUnit.days.4=dny

emailVerificationBodyCode=OvÄÅte prosÃ­m svou e-mailovou adresu zadÃ¡nÃ­m nÃ¡sledujÃ­cÃ­ho kÃ³du.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>OvÄÅte prosÃ­m svou e-mailovou adresu zadÃ¡nÃ­m nÃ¡sledujÃ­cÃ­ho kÃ³du.</p><p><b>{0}</b></p>

