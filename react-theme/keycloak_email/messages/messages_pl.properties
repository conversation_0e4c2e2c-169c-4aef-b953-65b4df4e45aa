# encoding: UTF-8
emailVerificationSubject=Zweryfikuj email
emailVerificationBody=KtoÅ utworzyÅ juÅ¼ konto {2} z tym adresem e-mail. <PERSON><PERSON><PERSON> to <PERSON>, kliknij poniÅ¼szy link, aby zweryfikowaÄ swÃ³j adres e-mail \n\n{0}\n\nLink ten wygaÅnie w ciÄgu {3}.\n\nJeÅli nie utworzyÅeÅ tego konta, po prostu zignoruj tÄ wiadomoÅÄ.
emailVerificationBodyHtml=<p>K<PERSON>Å utworzyÅ juÅ¼ konto {2} z tym adresem e-mail. Je<PERSON><PERSON> to Ty, kliknij <a href="{0}">ten link</a> aby zweryfikowaÄ swÃ³j adres e-mail</p><p>Link ten wygaÅnie w ciÄgu {3}</p><p>JeÅli nie utworzyÅeÅ tego konta, po prostu zignoruj tÄ wiadomoÅÄ.</p>
emailTestSubject=[KEYCLOAK] - wiadomoÅÄ testowa SMTP
emailTestBody=To jest wiadomoÅÄ testowa
emailTestBodyHtml=<p>To jest wiadomoÅÄ testowa</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=KtoÅ chce poÅÄczyÄ Twoje konto "{1}" z kontem "{0}" uÅ¼ytkownika {2}. JeÅli to Ty, kliknij poniÅ¼szy link by poÅÄczyÄ konta\n\n{3}\n\nTen link wygaÅnie w ciÄgu {5}.\n\nJeÅli nie chcesz poÅÄczyÄ konta to zignoruj tÄ wiadomoÅÄ. JeÅli poÅÄczysz konta, bÄdziesz mÃ³gÅ siÄ zalogowaÄ na {1} przez {0}.
identityProviderLinkBodyHtml=<p>KtoÅ chce poÅÄczyÄ Twoje konto <b>{1}</b> z kontem <b>{0}</b> uÅ¼ytkownika {2}. JeÅli to Ty, kliknij <a href="{3}">ten link</a> by poÅÄczyÄ konta.</p><p>Ten link wygaÅnie w ciÄgu {5}.</p><p>JeÅli nie chcesz poÅÄczyÄ konta to zignoruj tÄ wiadomoÅÄ. JeÅli poÅÄczysz konta, bÄdziesz mÃ³gÅ siÄ zalogowaÄ na {1} przez {0}.</p>
passwordResetSubject=Zresetuj hasÅo
passwordResetBody=KtoÅ wÅaÅnie poprosiÅ o zmianÄ danych logowania Twojego konta {2}. JeÅli to Ty, kliknij poniÅ¼szy link, aby je zresetowaÄ.\n\n{0}\n\nTen link i kod stracÄ waÅ¼noÅÄ w ciÄgu {3}.\n\nJeÅli nie chcesz zresetowaÄ swoich danych logowania, po prostu zignoruj tÄ wiadomoÅÄ i nic siÄ nie zmieni.
passwordResetBodyHtml=<p>KtoÅ wÅaÅnie poprosiÅ o zmianÄ poÅwiadczeÅ Twojego konta {2}. JeÅli to Ty, kliknij poniÅ¼szy link, aby je zresetowaÄ.</p><p><a href="{0}">Link do resetowania poÅwiadczeÅ</a></p><p>Ten link wygaÅnie w ciÄgu {3}.</p><p>JeÅli nie chcesz resetowaÄ swoich poÅwiadczeÅ, po prostu zignoruj tÄ wiadomoÅÄ i nic siÄ nie zmieni.</p>
executeActionsSubject=Zaktualizuj swoje konto
executeActionsBody=Administrator wÅaÅnie zaÅ¼ÄdaÅ aktualizacji konta {2} poprzez wykonanie nastÄpujÄcych dziaÅaÅ: {3}. Kliknij poniÅ¼szy link, aby rozpoczÄÄ ten proces.\n\n{0}\n\nTen link wygaÅnie w ciÄgu {4}.\n\nJeÅli nie masz pewnoÅci, Å¼e administrator tego zaÅ¼ÄdaÅ, po prostu zignoruj tÄ wiadomoÅÄ i nic siÄ nie zmieni.
executeActionsBodyHtml=<p>Administrator wÅaÅnie zaÅ¼ÄdaÅ aktualizacji konta {2} poprzez wykonanie nastÄpujÄcych dziaÅaÅ: {3}. Kliknij <a href="{0}">ten link</a>, aby rozpoczÄÄ proces.</p><p>Link ten wygaÅnie w ciÄgu {4}.</p><p>JeÅli nie masz pewnoÅci, Å¼e administrator tego zaÅ¼ÄdaÅ, po prostu zignoruj tÄ wiadomoÅÄ i nic siÄ nie zmieni.</p>
eventLoginErrorSubject=BÅÄd logowania
eventLoginErrorBody=Nieudana prÃ³ba logowania zostaÅa wykryta na Twoim koncie {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.
eventLoginErrorBodyHtml=<p>Nieudana prÃ³ba logowania zostaÅa wykryta na Twoim koncie {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.</p>
eventRemoveTotpSubject=UsuÅ hasÅo jednorazowe (OTP)
eventRemoveTotpBody=HasÅo jednorazowe (OTP) zostaÅo usuniÄte z Twojego konta w {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.
eventRemoveTotpBodyHtml=<p>HasÅo jednorazowe (OTP) zostaÅo usuniÄte z Twojego konta w {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.</p>
eventUpdatePasswordSubject=Aktualizuj hasÅo
eventUpdatePasswordBody=Twoje hasÅo zostaÅo zmienione {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.
eventUpdatePasswordBodyHtml=<p>Twoje hasÅo zostaÅo zmienione {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.</p>
eventUpdateTotpSubject=Aktualizuj hasÅo jednorazowe (OTP)
eventUpdateTotpBody=HasÅo jednorazowe (OTP) zostaÅo zaktualizowane na Twoim koncie {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.
eventUpdateTotpBodyHtml=<p>HasÅo jednorazowe (OTP) zostaÅo zaktualizowane na Twoim koncie {0} z {1}. JeÅli to nie Ty, skontaktuj siÄ z administratorem.</p>

requiredAction.CONFIGURE_TOTP=Konfiguracja hasÅa jednorazowego (OTP)
requiredAction.terms_and_conditions=Regulamin
requiredAction.UPDATE_PASSWORD=Aktualizacja hasÅa
requiredAction.UPDATE_PROFILE=Aktualizacja profilu
requiredAction.VERIFY_EMAIL=Weryfikacja adresu e-mail

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=sekund
linkExpirationFormatter.timePeriodUnit.seconds.1=sekunda
linkExpirationFormatter.timePeriodUnit.seconds.2=sekundy
linkExpirationFormatter.timePeriodUnit.seconds.3=sekundy
linkExpirationFormatter.timePeriodUnit.seconds.4=sekundy
linkExpirationFormatter.timePeriodUnit.minutes=minut
linkExpirationFormatter.timePeriodUnit.minutes.1=minuta
linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=godzin
linkExpirationFormatter.timePeriodUnit.hours.1=godzina
linkExpirationFormatter.timePeriodUnit.hours.2=godziny
linkExpirationFormatter.timePeriodUnit.hours.3=godziny
linkExpirationFormatter.timePeriodUnit.hours.4=godziny
linkExpirationFormatter.timePeriodUnit.days=dni
linkExpirationFormatter.timePeriodUnit.days.1=dzieÅ

emailVerificationBodyCode=PotwierdÅº swÃ³j adres e-mail wprowadzajÄc nastÄpujÄcy kod.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>PotwierdÅº swÃ³j adres e-mail, wprowadzajÄc nastÄpujÄcy kod.</p><p><b>{0}</b></p>
