# encoding: utf-8
emailVerificationSubject=éªè¯çµå­é®ä»¶
emailVerificationBody=ç¨æ·ä½¿ç¨å½åçµå­é®ä»¶æ³¨å {2} è´¦æ·ãå¦æ¯æ¬äººæä½ï¼è¯·ç¹å»ä»¥ä¸é¾æ¥å®æé®ç®±éªè¯\n\n{0}\n\nè¿ä¸ªé¾æ¥ä¼å¨ {1} å<PERSON>åè¿æ.\n\nå¦ææ¨æ²¡ææ³¨åç¨æ·ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ã
emailVerificationBodyHtml=<p>ç¨æ·ä½¿ç¨å½åçµå­é®ä»¶æ³¨å {2} è´¦æ·ãå¦æ¯æ¬äººæä½ï¼è¯·ç¹å»ä»¥ä¸é¾æ¥å®æé®ç®±éªè¯</p><p><a href="{0}">{0}</a></p><p>è¿ä¸ªé¾æ¥ä¼å¨ {1} åéåè¿æ.</p><p>å¦ææ¨æ²¡ææ³¨åç¨æ·ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ã</p>
identityProviderLinkSubject=é¾æ¥ {0}
identityProviderLinkBody=æç¨æ·æ³è¦å°è´¦æ· "{1}" ä¸ç¨æ·{2}çè´¦æ·"{0}" åé¾æ¥ . å¦ææ¯æ¬äººæä½ï¼è¯·ç¹å»ä»¥ä¸é¾æ¥å®æé¾æ¥è¯·æ±\n\n{3}\n\nè¿ä¸ªé¾æ¥ä¼å¨ {4} å<PERSON><PERSON>è¿æ.\n\nå¦éæ¬äººæä½ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ãå¦ææ¨é¾æ¥è´¦æ·ï¼æ¨å°å¯ä»¥éè¿{0}ç»å½è´¦æ· {1}.
identityProviderLinkBodyHtml=<p>æç¨æ·æ³è¦å°è´¦æ· <b>{1}</b> ä¸ç¨æ·{2} çè´¦æ·<b>{0}</b>  åé¾æ¥ . å¦ææ¯æ¬äººæä½ï¼è¯·ç¹å»ä»¥ä¸é¾æ¥å®æé¾æ¥è¯·æ±</p><p><a href="{3}">{3}</a></p><p>è¿ä¸ªé¾æ¥ä¼å¨ {4} åéåè¿æã</p><p>å¦éæ¬äººæä½ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ãå¦ææ¨é¾æ¥è´¦æ·ï¼æ¨å°å¯ä»¥éè¿{0}ç»å½è´¦æ· {1}.</p>
passwordResetSubject=éç½®å¯ç 
passwordResetBody=æç¨æ·è¦æ±ä¿®æ¹è´¦æ· {2} çå¯ç .å¦æ¯æ¬äººæä½ï¼è¯·ç¹å»ä¸é¢é¾æ¥è¿è¡éç½®.\n\n{0}\n\nè¿ä¸ªé¾æ¥ä¼å¨ {1} åéåè¿æ.\n\nå¦ææ¨ä¸æ³éç½®æ¨çå¯ç ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ï¼å¯ç ä¸ä¼æ¹åã
passwordResetBodyHtml=<p>æç¨æ·è¦æ±ä¿®æ¹è´¦æ· {2} çå¯ç å¦æ¯æ¬äººæä½ï¼è¯·ç¹å»ä¸é¢é¾æ¥è¿è¡éç½®.</p><p><a href="{0}">{0}</a></p><p>è¿ä¸ªé¾æ¥ä¼å¨ {1} åéåè¿æ</p><p>å¦ææ¨ä¸æ³éç½®æ¨çå¯ç ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ï¼å¯ç ä¸ä¼æ¹åã</p>
executeActionsSubject=æ´æ°æ¨çè´¦æ·
executeActionsBody=æ¨çç®¡çåè¦æ±æ¨æ´æ°è´¦æ· {2}. ç¹å»ä»¥ä¸é¾æ¥å¼å§æ´æ°\n\n{0}\n\nè¿ä¸ªé¾æ¥ä¼å¨ {1} åéåå¤±æ.\n\nå¦ææ¨ä¸ç¥éç®¡çåè¦æ±æ´æ°è´¦æ·ä¿¡æ¯ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ãè´¦æ·ä¿¡æ¯ä¸ä¼ä¿®æ¹ã
executeActionsBodyHtml=<p>æ¨çç®¡çåè¦æ±æ¨æ´æ°è´¦æ·{2}.  ç¹å»ä»¥ä¸é¾æ¥å¼å§æ´æ°.</p><p><a href="{0}">{0}</a></p><p>è¿ä¸ªé¾æ¥ä¼å¨ {1} åéåå¤±æ.</p><p>å¦ææ¨ä¸ç¥éç®¡çåè¦æ±æ´æ°è´¦æ·ä¿¡æ¯ï¼è¯·å¿½ç¥è¿æ¡æ¶æ¯ãè´¦æ·ä¿¡æ¯ä¸ä¼ä¿®æ¹ã</p>
eventLoginErrorSubject=ç»å½éè¯¯
eventLoginErrorBody=å¨{0} ç± {1}ä½¿ç¨æ¨çè´¦æ·ç»å½å¤±è´¥. å¦æè¿ä¸æ¯æ¨æ¬äººæä½ï¼è¯·èç³»ç®¡çå.
eventLoginErrorBodyHtml=<p>å¨{0} ç± {1}ä½¿ç¨æ¨çè´¦æ·ç»å½å¤±è´¥. å¦æè¿ä¸æ¯æ¨æ¬äººæä½ï¼è¯·èç³»ç®¡çå.</p>
eventRemoveTotpSubject=å é¤ OTP
eventRemoveTotpBody=OTPå¨ {0} ç±{1} ä»æ¨çè´¦æ·ä¸­å é¤.å¦æè¿ä¸æ¯æ¨æ¬äººæä½ï¼è¯·èç³»ç®¡çå
eventRemoveTotpBodyHtml=<p>OTPå¨ {0} ç±{1} ä»æ¨çè´¦æ·ä¸­å é¤.å¦æè¿ä¸æ¯æ¨æ¬äººæä½ï¼è¯·èç³»ç®¡çåã</p>
eventUpdatePasswordSubject=æ´æ°å¯ç 
eventUpdatePasswordBody=æ¨çå¯ç å¨{0} ç± {1}æ´æ¹. å¦éæ¬äººæä½ï¼è¯·èç³»ç®¡çå
eventUpdatePasswordBodyHtml=<p>æ¨çå¯ç å¨{0} ç± {1}æ´æ¹. å¦éæ¬äººæä½ï¼è¯·èç³»ç®¡çå</p>
eventUpdateTotpSubject=æ´æ° OTP
eventUpdateTotpBody=æ¨è´¦æ·çOTP éç½®å¨{0} ç± {1}æ´æ¹. å¦éæ¬äººæä½ï¼è¯·èç³»ç®¡çåã
eventUpdateTotpBodyHtml=<p>æ¨è´¦æ·çOTP éç½®å¨{0} ç± {1}æ´æ¹. å¦éæ¬äººæä½ï¼è¯·èç³»ç®¡çåã</p>
