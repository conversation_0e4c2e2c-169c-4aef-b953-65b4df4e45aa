# encoding: UTF-8
emailVerificationSubject=Vahvista sÃ¤hkÃ¶posti
emailVerificationBody=TÃ¤llÃ¤ sÃ¤hkÃ¶postiosoitteella on luotu {2}-tili. Jos loit tilin itse, klikkaa alla olevaa linkkiÃ¤ vahvistaaksesi sÃ¤hkÃ¶postiosoitteesi\n\n{0}\n\nLinkin vanhenemisaika: {3}.\n\nJos et luonut tÃ¤tÃ¤ tiliÃ¤, jÃ¤tÃ¤ viesti huomiotta.
emailVerificationBodyHtml=<p>TÃ¤llÃ¤ sÃ¤hkÃ¶postiosoitteella on luotu {2}-tili. Jos loit tilin itse, klikkaa alla olevaa linkkiÃ¤ vahvistaaksesi sÃ¤hkÃ¶postiosoitteesi</p><p><a href="{0}"><PERSON><PERSON> vahvistamiseen</a></p><p>Linkin vanhenemisaika: {3}.</p><p>Jos et luonut tÃ¤tÃ¤ tiliÃ¤, jÃ¤tÃ¤ viesti huomiotta.</p>
emailTestSubject=[KEYCLOAK] - SMTP testiviesti
emailTestBody=TÃ¤mÃ¤ on testiviesti
emailTestBodyHtml=<p>TÃ¤mÃ¤ on testiviesti</p>
identityProviderLinkSubject=Linkki {0}
identityProviderLinkBody=Saimme pyynnÃ¶n linkittÃ¤Ã¤ "{1}"-tilin "{0}"-tiliin kÃ¤yttÃ¤jÃ¤lle {2}. Jo<PERSON> teit tÃ¤mÃ¤n pyynnÃ¶n, klikkaa alla olevaa linkkiÃ¤ tilien linkittÃ¤miseksi\n\n{3}\n\nLinkin vanhenemisaika: {5}.\n\nJos et halua linkittÃ¤Ã¤ tilejÃ¤, jÃ¤tÃ¤ tÃ¤mÃ¤ viesti huomiotta. Jos linkitÃ¤t tilit, voit jatkossa kirjautua tilille {1}, tilin {0} kautta.
identityProviderLinkBodyHtml=<p>Saimme pyynnÃ¶n linkittÃ¤Ã¤  <b>{1}</b>-tilin <b>{0}</b>-tiliin kÃ¤yttÃ¤jÃ¤lle {2}. Jos teit tÃ¤mÃ¤n pyynnÃ¶n, klikkaa alla olevaa linkkiÃ¤ tilien linkittÃ¤miseksi</p><p><a href="{3}">Vahvista tilien linkitys</a></p><p>Linkin vanhenemisaika: {5}.</p><p>Jos et halua linkittÃ¤Ã¤ tilejÃ¤, jÃ¤tÃ¤ tÃ¤mÃ¤ viesti huomiotta. Jos linkitÃ¤t tilit, voit jatkossa kirjautua tilille {1}, tilin {0} kautta.</p>
passwordResetSubject=Salasanan nollaus
passwordResetBody=Saimme pyynnÃ¶n vaihtaa {2}-tilisi salasanan. Jos pyysit itse salasanan vaihtoa, pÃ¤Ã¤set tÃ¤stÃ¤ linkistÃ¤ vaihtamaan salasanasi:\n\n{0}\n\nLinkin vanhenemisaika: {3} .\n\nJos et halua vaihtaa salasanaasi tai et ole pyytÃ¤nyt salasanan vaihtoa itse, jÃ¤tÃ¤ tÃ¤mÃ¤n viesti huomiotta.
passwordResetBodyHtml=<p>Saimme pyynnÃ¶n vaihtaa {2}-tilisi salasanan. Jos pyysit itse salasanan vaihtoa, pÃ¤Ã¤set tÃ¤stÃ¤ linkistÃ¤ vaihtamaan salasanasi:</p><p><a href="{0}">Linkki salasanan vaihtoon</a></p><p>Linkin vanhenemisaika: <strong>{3}</strong>.</p><p>Jos et halua vaihtaa salasanaasi tai et ole pyytÃ¤nyt salasanan vaihtoa itse, jÃ¤tÃ¤ tÃ¤mÃ¤n viesti huomiotta.</p>
executeActionsSubject=PÃ¤ivitÃ¤ tilisi
executeActionsBody=JÃ¤rjestelmÃ¤nvalvoja on pyytÃ¤nyt sinua pÃ¤ivittÃ¤mÃ¤Ã¤n {2}-tilisi tekemÃ¤llÃ¤ seuraavat toimenpiteet: {3}. Aloita prosessi klikkaamalla alla olevaa linkkiÃ¤.\n\n{0}\n\nLinkin vanhenemisaika: {4}.\n\nJos et ole tietoinen tÃ¤stÃ¤ jÃ¤rjestelmÃ¤nvalvojan pyynnÃ¶stÃ¤, jÃ¤tÃ¤ tÃ¤mÃ¤n viesti huomiotta.
executeActionsBodyHtml=<p>JÃ¤rjestelmÃ¤nvalvoja on pyytÃ¤nyt sinua pÃ¤ivittÃ¤mÃ¤Ã¤n {2}-tilisi tekemÃ¤llÃ¤ seuraavat toimenpiteet: {3}. Aloita prosessi klikkaamalla alla olevaa linkkiÃ¤.</p><p><a href="{0}">Linkki tilin pÃ¤ivittÃ¤miseen</a></p><p>Linkin vanhenemisaika: {4}.</p><p>Jos et ole tietoinen tÃ¤stÃ¤ jÃ¤rjestelmÃ¤nvalvojan pyynnÃ¶stÃ¤, jÃ¤tÃ¤ tÃ¤mÃ¤n viesti huomiotta.</p>
eventLoginErrorSubject=Kirjautuminen epÃ¤onnistui
eventLoginErrorBody=TilillÃ¤nne on havaittu epÃ¤onnistunut kirjautumisyritys {0} osoitteesta {1}. Jos et itse yrittÃ¤nyt kirjautua tilillesi, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.
eventLoginErrorBodyHtml=<p>TilillÃ¤nne on havaittu epÃ¤onnistunut kirjautumisyritys {0} osoitteesta {1}. Jos et itse yrittÃ¤nyt kirjautua tilillesi, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.</p>
eventRemoveTotpSubject=Poista OTP
eventRemoveTotpBody=OTP on poistettu tililtÃ¤si {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.
eventRemoveTotpBodyHtml=<p>OTP on poistettu tililtÃ¤si {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.</p>
eventUpdatePasswordSubject=PÃ¤ivitÃ¤ salasana
eventUpdatePasswordBody=Tilisi salasana on vaihdettu {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan..
eventUpdatePasswordBodyHtml=<p>Tilisi salasana on vaihdettu {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.</p>
eventUpdateTotpSubject=PÃ¤ivitÃ¤ OTP
eventUpdateTotpBody=OTP on pÃ¤ivitetty tilillesi {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.
eventUpdateTotpBodyHtml=<p>OTP on pÃ¤ivitetty tilillesi {0} osoitteesta {1}. Jos et itse tehnyt tÃ¤tÃ¤, ota yhteyttÃ¤ jÃ¤rjestelmÃ¤nvalvojaan.</p>
requiredAction.CONFIGURE_TOTP=Konfiguroi OTP
requiredAction.terms_and_conditions=KÃ¤yttÃ¶ehdot
requiredAction.UPDATE_PASSWORD=PÃ¤ivitÃ¤ salasana
requiredAction.UPDATE_PROFILE=PÃ¤ivitÃ¤ profiili
requiredAction.VERIFY_EMAIL=Vahvista sÃ¤hkÃ¶posti

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=sekuntia
linkExpirationFormatter.timePeriodUnit.seconds.1=sekunti
linkExpirationFormatter.timePeriodUnit.minutes=minuuttia
linkExpirationFormatter.timePeriodUnit.minutes.1=minuutti
linkExpirationFormatter.timePeriodUnit.hours=tuntia
linkExpirationFormatter.timePeriodUnit.hours.1=tunti
linkExpirationFormatter.timePeriodUnit.days=pÃ¤ivÃ¤Ã¤
linkExpirationFormatter.timePeriodUnit.days.1=pÃ¤ivÃ¤

emailVerificationBodyCode=Ole hyvÃ¤ ja vahvista sÃ¤hkÃ¶postiosoitteesi alla olevalla koodilla.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Ole hyvÃ¤ ja vahvista sÃ¤hkÃ¶postiosoitteesi alla olevalla koodilla.</p><p><b>{0}</b></p>