# encoding: UTF-8
emailVerificationSubject=Verificer email
emailVerificationBody=Nogen har oprettet en {2} konto med denne email adresse. Hvis dette var dig, bedes du trykke pÃ¥ forbindet herunder for at verificere din email adresse \n\n{0}\n\nDette link vil udlÃ¸be inden for {3}.\n\nHvis det var dig der har oprettet denne konto, bedes du se bort fra denne mail.
emailVerificationBodyHtml=<p>Nogen har oprettet en {2} konto med denne email adresse. Hvis dette var dig, bedes du trykke pÃ¥ forbindet herunder for at verificere din email adresse</p><p><a href="{0}">Link til email verificering</a></p><p>Dette link vil udlÃ¸be inden for {3}.</p><p>Hvis det var dig der har oprettet denne konto, bedes du se bort fra denne mail.</p>
emailTestSubject=[KEYCLOAK] - SMTP test besked
emailTestBody=Dette er en test besked
emailTestBodyHtml=<p>Dette er en test besked</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Nogen vil forbinde din "{1}" konto med "{0}" kontoen som er tilknyttet brugeren {2}. Hvis dette var dig, bedes du klikke pÃ¥ forbindet herunder for at forbinde de to konti\n\n{3}\n\nDette link vil udlÃ¸be efter {5}.\n\nHvis du ikke vil forbinde disse konti, kan du bare ignore denne besked. Hvis du vÃ¦lger at forbinde de to konti, kan du logge ind som {1} via {0}.
identityProviderLinkBodyHtml=<p>Someone wants to link your <b>{1}</b> account with <b>{0}</b> account of user {2} . Hvis dette var dig, bedes du klikke pÃ¥ forbindet herunder for at forbinde de to konti</p><p><a href="{3}">BekrÃ¦ft</a></p><p>Dette link vil udlÃ¸be efter {5}.</p><p>nHvis du ikke vil forbinde disse konti, kan du bare ignore denne besked. Hvis du vÃ¦lger at forbinde de to konti, kan du logge ind som {1} via {0}.
passwordResetSubject=Gendan adgangskode
passwordResetBody=Nogen har forsÃ¸gt at nulstille adgangskoden til {2}. Hvis dette var dig, bedes du klikke pÃ¥ linket herunder for at nulstille adgangskoden.\n\n{0}\n\nDette link og kode vil udlÃ¸be efter {3}.\n\nHvis du ikke Ã¸nsker at nulstille din adgangskode, kan du se bort fra denne besked.
passwordResetBodyHtml=<p>Nogen har forsÃ¸gt at nulstille adgangskoden til {2}. Hvis dette var dig, bedes du klikke pÃ¥ linket herunder for at nulstille adgangskoden.</p><p><a href="{0}">Nulstil adgangskode</a></p><p>Dette link og kode vil udlÃ¸be efter {3}.</p><p>Hvis du ikke Ã¸nsker at nulstille din adgangskode, kan du se bort fra denne besked.</p>
executeActionsSubject=Opdater din konto
executeActionsBody=Din administrator beder dig opdatere din {2} konto ved at udfÃ¸re fÃ¸lgende handling(er): {3}. Klik pÃ¥ linket herunder for at starte processen.\n\n{0}\n\nDette link udlÃ¸ber efter {4}.\n\nHvis du ikke mener at din administrator har efterspurgt dette, kan du blot se bort fra denne besked.
executeActionsBodyHtml=<p>Din administrator beder dig opdatere din {2} konto ved at udfÃ¸re fÃ¸lgende handling(er): {3}. Klik pÃ¥ linket herunder for at starte processen.</p><p><a href="{0}">Opdater konto</a></p><p>Dette link udlÃ¸ber efter {4}.</p><p>Hvis du ikke mener at din administrator har efterspurgt dette, kan du blot se bort fra denne besked.</p>
eventLoginErrorSubject=Logind fejl
eventLoginErrorBody=Et fejlet logind forsÃ¸g er blevet registreret pÃ¥ din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.
eventLoginErrorBodyHtml=<p>Et fejlet logind forsÃ¸g er blevet registreret pÃ¥ din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.</p>
eventRemoveTotpSubject=Fjern OTP
eventRemoveTotpBody=OTP er blevet fjernet fra din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.
eventRemoveTotpBodyHtml=<p>OTP er blevet fjernet fra din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.</p>
eventUpdatePasswordSubject=Opdater adgangskode
eventUpdatePasswordBody=Din adgangskode er blevet opdateret d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.
eventUpdatePasswordBodyHtml=<p>Din adgangskode er blevet opdateret d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.</p>
eventUpdateTotpSubject=Opdater OTP
eventUpdateTotpBody=OTP blev opdateret pÃ¥ din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.
eventUpdateTotpBodyHtml=<p>OTP blev opdateret pÃ¥ din konto d. {0} fra {1}. Hvis dette ikke var dig, bedes du kontakte din administrator omgÃ¥ende.</p>

requiredAction.CONFIGURE_TOTP=Konfigurer OTP
requiredAction.terms_and_conditions=VilkÃ¥r og Betingelser
requiredAction.UPDATE_PASSWORD=Opdater Adgangskode
requiredAction.UPDATE_PROFILE=Opdater Profil
requiredAction.VERIFY_EMAIL=Verificer Email

# units for link expiration timeout formatting
forbindexpirationFormatter.timePeriodUnit.seconds=sekunder
forbindexpirationFormatter.timePeriodUnit.seconds.1=sekund
forbindexpirationFormatter.timePeriodUnit.minutes=minutter
forbindexpirationFormatter.timePeriodUnit.minutes.1=minut
forbindexpirationFormatter.timePeriodUnit.hours=timer
forbindexpirationFormatter.timePeriodUnit.hours.1=time
forbindexpirationFormatter.timePeriodUnit.days=dage
forbindexpirationFormatter.timePeriodUnit.days.1=dag

emailVerificationBodyCode=Verificer din email adresse ved at indtaste fÃ¸lgende kode.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verificer din email adresse ved at indtaste fÃ¸lgende kode.</p><p><b>{0}</b></p>
