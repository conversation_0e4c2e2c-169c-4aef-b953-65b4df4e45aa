emailVerificationSubject=E-postay\u0131 do\u011Frula
emailVerificationBody=Birisi bu e-posta adresiyle bir {2} hesap olu\u015Fturdu. Bu sizseniz, e-posta adresinizi do\u011Frulamak i\u00E7in a\u015Fa\u011F\u0131daki ba\u011Flant\u0131ya t\u0131klay\u0131n\n\n{0}\n\nBu ba\u011Flant\u0131 {3} i\u00E7inde sona erecek.\n\nBu hesab\u0131 olu\u015Fturmad\u0131ysan\u0131z, sadece bu iletiyi yoksay\u0131n\u0131z.
emailVerificationBodyHtml=<p>Birisi bu e-posta adresiyle bir {2} hesap olu\u015Fturdu. Bu sizseniz, e-posta adresinizi do\u011Frulamak i\u00E7in a\u015Fa\u011F\u0131daki ba\u011Flant\u0131y\u0131 t\u0131klay\u0131n.</p><p><a href="{0}">E-posta adresi do\u011Frulama adresi</a></p><p>Bu ba\u011Flant\u0131n\u0131n s\u00FCresi {3} i\u00E7erisinde sona erecek.</p><p>Bu hesab\u0131 siz olu\u015Fturmad\u0131ysan\u0131z, bu mesaj\u0131 g\u00F6z ard\u0131 edin.</p>
emailTestSubject=[KEYCLOAK] - SMTP test mesaj\u0131
emailTestBody=Bu bir test mesaj\u0131
emailTestBodyHtml=<p>Bu bir test mesaj\u0131</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Birisi "{1}" hesab\u0131n\u0131z\u0131 "{0}" kullan\u0131c\u0131 hesab\u0131 {2} ile ba\u011Flamak istiyor. Bu sizseniz, hesaplar\u0131 ba\u011Flamak i\u00E7in a\u015Fa\u011F\u0131daki ba\u011Flant\u0131y\u0131 t\u0131klay\u0131n:\n\n{3}\n\nBu ba\u011Flant\u0131 {5} i\u00E7inde sona erecek.\n\nHesab\u0131n\u0131z\u0131 ba\u011Flamak istemiyorsan\u0131z bu mesaj\u0131 g\u00F6z ard\u0131 edin. Hesaplar\u0131 ba\u011Flarsan\u0131z, {1} ile {0} aras\u0131nda oturum a\u00E7abilirsiniz.
identityProviderLinkBodyHtml=<p>Birisi <b> {1} </ b> hesab\u0131n\u0131z\u0131 {2} kullan\u0131c\u0131s\u0131 <b> {0} </ b> hesab\u0131na ba\u011Flamak istiyor. Bu sizseniz, ba\u011Flant\u0131 vermek i\u00E7in a\u015Fa\u011F\u0131daki ba\u011Flant\u0131y\u0131 t\u0131klay\u0131n</p><p><a href="{3}">Hesap ba\u011Flant\u0131s\u0131n\u0131 onaylamak i\u00E7in ba\u011Flant\u0131</a></p><p>Bu ba\u011Flant\u0131n\u0131n s\u00FCresi {5} i\u00E7erisinde sona erecek.</p><p>Hesab\u0131 ba\u011Flamak istemiyorsan\u0131z, bu mesaj\u0131 g\u00F6z ard\u0131 edin. Hesaplar\u0131 ba\u011Flarsan\u0131z, {1} ile {0} aras\u0131nda oturum a\u00E7abilirsiniz.</p>
passwordResetSubject=\u015Eifreyi s\u0131f\u0131rla
passwordResetBody=Birisi, {2} hesab\u0131n\u0131z\u0131n kimlik bilgilerini de\u011Fi\u015Ftirmeyi istedi.Bu sizseniz, s\u0131f\u0131rlamak i\u00E7in a\u015Fa\u011F\u0131daki ba\u011Flant\u0131y\u0131 t\u0131klay\u0131n.\n\n{0}\n\nBu ba\u011Flant\u0131 ve kod {3} i\u00E7inde sona erecek.\n\nFakat bilgilerinizi s\u0131f\u0131rlamak istemiyorsan\u0131z, Sadece bu mesaj\u0131 g\u00F6rmezden gelin ve hi\u00E7bir \u015Fey de\u011Fi\u015Fmeyecek.
passwordResetBodyHtml=<p>Birisi, {2} hesab\u0131n\u0131z\u0131n kimlik bilgilerini de\u011Fi\u015Ftirmeyi istedi. Sizseniz, s\u0131f\u0131rlamak i\u00E7in a\u015Fa\u011F\u0131daki linke t\u0131klay\u0131n\u0131z.</p><p><a href="{0}">Kimlik bilgilerini s\u0131f\u0131rlamak i\u00E7in ba\u011Flant\u0131</a></p><p>Bu ba\u011Flant\u0131n\u0131n s\u00FCresi {3} i\u00E7erisinde sona erecek.</p><p>Kimlik bilgilerinizi s\u0131f\u0131rlamak istemiyorsan\u0131z, bu mesaj\u0131 g\u00F6z ard\u0131 edin.</p>
executeActionsSubject=Hesab\u0131n\u0131z\u0131 G\u00FCncelleyin
executeActionsBody=Y\u00F6neticiniz a\u015Fa\u011F\u0131daki i\u015Flemleri ger\u00E7ekle\u015Ftirerek {2} hesab\u0131n\u0131z\u0131 g\u00FCncelledi: {3}. Bu i\u015Flemi ba\u015Flatmak i\u00E7in a\u015Fa\u011F\u0131daki linke t\u0131klay\u0131n.\n\n{0}\n\nBu ba\u011Flant\u0131n\u0131n s\u00FCresi {4} i\u00E7erisinde sona erecek.\n\nY\u00F6neticinizin bunu istedi\u011Finden habersizseniz, bu mesaj\u0131 g\u00F6z ard\u0131 edin ve hi\u00E7bir \u015Fey de\u011Fi\u015Fmez.
executeActionsBodyHtml=<p>Y\u00F6neticiniz a\u015Fa\u011F\u0131daki i\u015Flemleri ger\u00E7ekle\u015Ftirerek {2} hesab\u0131n\u0131z\u0131 g\u00FCncelledi: {3}. Bu i\u015Flemi ba\u015Flatmak i\u00E7in a\u015Fa\u011F\u0131daki linke t\u0131klay\u0131n.</p><p><a href="{0}">Hesap g\u00FCncelleme ba\u011Flant\u0131s\u0131</a></p><p>Bu ba\u011Flant\u0131n\u0131n s\u00FCresi {4} i\u00E7erisinde sona erecek.</p><p>Y\u00F6neticinizin bunu istedi\u011Finden habersizseniz, bu mesaj\u0131 g\u00F6z ard\u0131 edin ve hi\u00E7bir \u015Fey de\u011Fi\u015Fmez.</p>
eventLoginErrorSubject=Giri\u015F hatas\u0131
eventLoginErrorBody={1} ''den {0} tarihinde ba\u015Far\u0131s\u0131z bir giri\u015F denemesi yap\u0131ld\u0131. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.
eventLoginErrorBodyHtml=<p>{1} ''den {0} tarihinde ba\u015Far\u0131s\u0131z bir giri\u015F denemesi yap\u0131ld\u0131. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.</p>
eventRemoveTotpSubject=OTP''yi kald\u0131r
eventRemoveTotpBody=OTP, {0} tarihinden {1} tarihinde hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.
eventRemoveTotpBodyHtml=<p>OTP, {0} tarihinden {1} tarihinde hesab\u0131n\u0131zdan kald\u0131r\u0131ld\u0131. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.</p>
eventUpdatePasswordSubject=\u015Eifreyi g\u00FCncelle
eventUpdatePasswordBody=\u015Eifreniz {0} tarihinde {0} tarihinde de\u011Fi\u015Ftirildi. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.
eventUpdatePasswordBodyHtml=<p>\u015Eifreniz {0} tarihinde {0} tarihinde de\u011Fi\u015Ftirildi. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.</p>
eventUpdateTotpSubject=OTP''yi G\u00FCncelle
eventUpdateTotpBody=OTP, {0} tarihinden {1} tarihinde hesab\u0131n\u0131z i\u00E7in g\u00FCncellendi. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.
eventUpdateTotpBodyHtml=<p>OTP, {0} tarihinden {1} tarihinde hesab\u0131n\u0131z i\u00E7in g\u00FCncellendi. Bu siz de\u011Filseniz, l\u00FCtfen y\u00F6neticiyle ileti\u015Fime ge\u00E7in.</p>

requiredAction.CONFIGURE_TOTP=OTP''yi yap\u0131land\u0131r
requiredAction.terms_and_conditions=\u015Eartlar ve Ko\u015Fullar
requiredAction.UPDATE_PASSWORD=\u015Eifre G\u00FCncelleme
requiredAction.UPDATE_PROFILE=Profilleri g\u00FCncelle
requiredAction.VERIFY_EMAIL=E-mail do\u011Frula

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=saniye
linkExpirationFormatter.timePeriodUnit.seconds.1=saniye
linkExpirationFormatter.timePeriodUnit.minutes=dakika
linkExpirationFormatter.timePeriodUnit.minutes.1=dakika
#for language which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like this:
#linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=saat
linkExpirationFormatter.timePeriodUnit.hours.1=saat
linkExpirationFormatter.timePeriodUnit.days=g\u00FCn
linkExpirationFormatter.timePeriodUnit.days.1=g\u00FCn

emailVerificationBodyCode=L\u00FCtfen a\u015Fa\u011F\u0131daki kodu girerek e-posta adresinizi do\u011Frulay\u0131n.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>L\u00FCtfen a\u015Fa\u011F\u0131daki kodu girerek e-posta adresinizi do\u011Frulay\u0131n.</p><p><b>{0}</b></p>

