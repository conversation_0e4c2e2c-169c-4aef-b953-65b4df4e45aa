emailVerificationSubject=Verifica\u00E7\u00E3o de endere\u00e7o de e-mail
emailVerificationBody=Algu\u00E9m criou uma conta {2} com este endere\u00E7o de e-mail. Se foi voc\u00EA, clique no link abaixo para verificar o seu endere\u00E7o de email.\n\n{0}\n\nEste link ir\u00E1 expirar dentro de {3}.\n\nSe n\u00E3o foi voc\u00EA quem criou esta conta, basta ignorar esta mensagem.
emailVerificationBodyHtml=<p>Algu\u00E9m criou uma conta {2} com este endere\u00E7o de e-mail. Se foi voc\u00EA, clique no link abaixo para verificar o seu endere\u00E7o de email.</p><p><a href="{0}">Link para verifica\u00e7\u00e3o de endere\u00e7o de e-mail</a></p><p>Este link ir\u00E1 expirar dentro de {3}.</p><p>Se n\u00E3o foi voc\u00EA quem criou esta conta, basta ignorar esta mensagem.</p>
emailTestSubject=[KEYCLOAK] - Mensagem de teste SMTP
emailTestBody=Esta \u00E9 uma mensagem de teste
emailTestBodyHtml=<p>Esta \u00E9 uma mensagem de teste</p>
identityProviderLinkSubject=Vincular {0}
identityProviderLinkBody=Algu\u00E9m quer vincular a sua conta "{1}" com a conta "{0}" do usu\u00E1rio {2} . Se foi voc\u00EA, clique no link abaixo para vincular as contas.\n\n{3}\n\nEste link ir\u00E1 expirar em {5}.\n\nSe voc\u00EA n\u00E3o quer vincular a conta, apenas ignore esta mensagem. Se voc\u00EA vincular as contas, voc\u00EA ser\u00E1 capaz de logar em {1} fazendo login em {0}.
identityProviderLinkBodyHtml=<p>Algu\u00E9m quer vincular a sua conta <b>{1}</b> com a conta <b>{0}</b> do usu\u00E1rio {2} . Se foi voc\u00EA, clique no link abaixo para vincular as contas.</p><p><a href="{3}">Link para confirmar vincula\u00e7\u00e3o de contas</a></p><p>Este link ir\u00E1 expirar em {5}.</p><p>Se voc\u00EA n\u00E3o quer vincular a conta, apenas ignore esta mensagem. Se voc\u00EA vincular as contas, voc\u00EA ser\u00E1 capaz de logar em {1} fazendo login em {0}.</p>
passwordResetSubject=Redefini\u00E7\u00E3o de senha
passwordResetBody=Algu\u00E9m solicitou uma altera\u00E7\u00E3o de senha da sua conta {2}. Se foi voc\u00EA, clique no link abaixo para redefini-la.\n\n{0}\n\nEste link e c\u00F3digo expiram em {3}.\n\nSe voc\u00EA n\u00E3o deseja redefinir sua senha, apenas ignore esta mensagem e nada ser\u00E1 alterado.
passwordResetBodyHtml=<p>Algu\u00E9m solicitou uma altera\u00E7\u00E3o de senha da sua conta {2}. Se foi voc\u00EA, clique no link abaixo para redefini-la.</p><p><a href="{0}">Link para redefinir a senha</a></p><p>Este link ir\u00E1 expirar em {3}.</p><p>Se voc\u00EA n\u00E3o deseja redefinir sua senha, apenas ignore esta mensagem e nada ser\u00E1 alterado.</p>
executeActionsSubject=Atualiza\u00E7\u00E3o de conta
executeActionsBody=Um administrador solicitou que voc\u00EA atualize sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique no link abaixo para iniciar o processo.\n\n{0}\n\nEste link ir\u00E1 expirar em {4}.\n\nSe voc\u00EA n\u00E3o tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada ser\u00E1 alterado.
executeActionsBodyHtml=<p>Um administrador solicitou que voc\u00EA atualize sua conta {2} com a(s) seguinte(s) etapa(s): {3}. Clique no link abaixo para iniciar o processo.</p><p><a href="{0}">Link para atualizar a conta</a></p><p>Este link ir\u00E1 expirar em {4}.</p><p>Se voc\u00EA n\u00E3o tem conhecimento de que o administrador solicitou isso, basta ignorar esta mensagem e nada ser\u00E1 alterado.</p>
eventLoginErrorSubject=Erro de login
eventLoginErrorBody=Uma tentativa de login malsucedida da sua conta foi detectada em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.
eventLoginErrorBodyHtml=<p>Uma tentativa de login malsucedida da sua conta foi detectada em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.</p>
eventRemoveTotpSubject=Remover autentica\u00e7\u00e3o de dois fatores
eventRemoveTotpBody=A autentica\u00e7\u00e3o de dois fatores foi removida da sua conta em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.
eventRemoveTotpBodyHtml=<p>A autentica\u00e7\u00e3o de dois fatores foi removida da sua conta em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.</p>
eventUpdatePasswordSubject=Atualiza\u00E7\u00E3o de senha
eventUpdatePasswordBody=Sua senha foi alterada em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.
eventUpdatePasswordBodyHtml=<p>Sua senha foi alterada em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.</p>
eventUpdateTotpSubject=Atualiza\u00E7\u00E3o de autentica\u00e7\u00e3o de dois fatores
eventUpdateTotpBody=A autentica\u00e7\u00e3o de dois fatores foi atualizada para a sua conta em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.
eventUpdateTotpBodyHtml=<p>A autentica\u00e7\u00e3o de dois fatores foi atualizada para a sua conta em {0} de {1}. Se n\u00E3o foi voc\u00EA, por favor, entre em contato com um administrador.</p>

requiredAction.CONFIGURE_TOTP=Configurar Autentica\u00e7\u00e3o de Dois Fatores
requiredAction.terms_and_conditions=Termos e Condi\u00E7\u00F5es
requiredAction.UPDATE_PASSWORD=Atualizar Senha
requiredAction.UPDATE_PROFILE=Atualizar Perfil
requiredAction.VERIFY_EMAIL=Verificar Endere\u00e7o de E-mail

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=segundos
linkExpirationFormatter.timePeriodUnit.seconds.1=segundo
linkExpirationFormatter.timePeriodUnit.minutes=minutos
linkExpirationFormatter.timePeriodUnit.minutes.1=minuto
#for language which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like this: 
#linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=horas
linkExpirationFormatter.timePeriodUnit.hours.1=hora
linkExpirationFormatter.timePeriodUnit.days=dias
linkExpirationFormatter.timePeriodUnit.days.1=dia

emailVerificationBodyCode=Verifique o seu endere\u00E7o de e-mail inserindo o seguinte c\u00F3digo.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Verifique o seu endere\u00E7o de e-mail inserindo o seguinte c\u00F3digo.</p><p><b>{0}</b></p>

