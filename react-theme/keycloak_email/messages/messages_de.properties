emailVerificationSubject=E-Mail verifizieren
emailVerificationBody=Je<PERSON> hat ein {2} Konto mit dieser E-Mail-Adresse erstellt. Falls Sie das waren, dann klicken Sie auf den Link, um die E-Mail-Adresse zu verifizieren.\n\n{0}\n\nDieser Link wird in {1} Minuten ablaufen.\n\nFalls Sie dieses Konto nicht erstellt haben, dann k\u00F6nnen sie diese Nachricht ignorieren.
emailVerificationBodyHtml=<p>Jemand hat ein {2} Konto mit dieser E-Mail-Adresse erstellt. Falls das Sie waren, klicken Sie auf den Link, um die E-Mail-Adresse zu verifizieren.</p><p><a href="{0}">Link zur Best\u00E4tigung der E-Mail-Adresse</a></p><p>Dieser Link wird in {1} Minuten ablaufen.</p><p>Falls Sie dieses Konto nicht erstellt haben, dann k\u00F6nnen sie diese Nachricht ignorieren.</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Es wurde beantragt Ihren Account {1} mit dem Account {0} von Benutzer {2} zu verlinken. Sollten Sie dies beantragt haben, klicken Sie auf den unten stehenden Link.\n\n{3}\n\n Die G\u00FCltigkeit des Links wird in {4} Minuten verfallen.\n\nSollten Sie Ihren Account nicht verlinken wollen, ignorieren Sie diese Nachricht. Wenn Sie die Accounts verlinken wird ein Login auf {1} \u00FCber {0} erm\u00F6glicht.
identityProviderLinkBodyHtml=<p>Es wurde beantragt Ihren Account {1} mit dem Account {0} von Benutzer {2} zu verlinken. Sollten Sie dies beantragt haben, klicken Sie auf den unten stehenden Link.</p><p><a href="{3}">Link zur Best\u00E4tigung der Kontoverkn\u00FCpfung</a></p><p>Die G\u00FCltigkeit des Links wird in {4} Minuten verfallen.</p><p>Sollten Sie Ihren Account nicht verlinken wollen, ignorieren Sie diese Nachricht. Wenn Sie die Accounts verlinken wird ein Login auf {1} \u00FCber {0} erm\u00F6glicht.</p>
passwordResetSubject=Passwort zur\u00FCcksetzen
passwordResetBody=Es wurde eine \u00C4nderung der Anmeldeinformationen f\u00FCr Ihren Account {2} angefordert. Wenn Sie diese \u00C4nderung beantragt haben, klicken Sie auf den unten stehenden Link.\n\n{0}\n\nDie G\u00FCltigkeit des Links wird in {1} Minuten verfallen.\n\nSollten Sie keine \u00C4nderung vollziehen wollen k\u00F6nnen Sie diese Nachricht ignorieren und an Ihrem Account wird nichts ge\u00E4ndert.
passwordResetBodyHtml=<p>Es wurde eine \u00C4nderung der Anmeldeinformationen f\u00FCr Ihren Account {2} angefordert. Wenn Sie diese \u00C4nderung beantragt haben, klicken Sie auf den unten stehenden Link.</p><p><a href="{0}">Link zum Zur\u00FCcksetzen von Anmeldeinformationen</a></p><p>Die G\u00FCltigkeit des Links wird in {1} Minuten verfallen.</p><p>Sollten Sie keine \u00C4nderung vollziehen wollen k\u00F6nnen Sie diese Nachricht ignorieren und an Ihrem Account wird nichts ge\u00E4ndert.</p>
executeActionsSubject=Aktualisieren Sie Ihr Konto
executeActionsBody=Ihr Administrator hat Sie aufgefordert Ihren Account {2} zu aktualisieren. Klicken Sie auf den unten stehenden Link um den Prozess zu starten.\n\n{0}\n\nDie G\u00FCltigkeit des Links wird in {1} Minuten verfallen.\n\nSollten Sie sich dieser Aufforderung nicht bewusst sein, ignorieren Sie diese Nachricht und Ihr Account bleibt unver\u00E4ndert.
executeActionsBodyHtml=<p>Ihr Administrator hat Sie aufgefordert Ihren Account {2} zu aktualisieren. Klicken Sie auf den unten stehenden Link um den Prozess zu starten.</p><p><a href="{0}">Link zum Account-Update</a></p><p>Die G\u00FCltigkeit des Links wird in {1} Minuten verfallen.</p><p>Sollten Sie sich dieser Aufforderung nicht bewusst sein, ignorieren Sie diese Nachricht und Ihr Account bleibt unver\u00E4ndert.</p>
eventLoginErrorSubject=Fehlgeschlagene Anmeldung
eventLoginErrorBody=Jemand hat um {0} von {1} versucht, sich mit Ihrem Konto anzumelden. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventLoginErrorBodyHtml=<p>Jemand hat um {0} von {1} versucht, sich mit Ihrem Konto anzumelden. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventRemoveTotpSubject=OTP Entfernt
eventRemoveTotpBody=OTP wurde von Ihrem Konto am {0} von {1} entfernt. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventRemoveTotpBodyHtml=<p>OTP wurde von Ihrem Konto am {0} von {1} entfernt. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventUpdatePasswordSubject=Passwort Aktualisiert
eventUpdatePasswordBody=Ihr Passwort wurde am {0} von {1} ge\u00E4ndert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventUpdatePasswordBodyHtml=<p>Ihr Passwort wurde am {0} von {1} ge\u00E4ndert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>
eventUpdateTotpSubject=OTP Aktualisiert
eventUpdateTotpBody=OTP wurde am {0} von {1} ge\u00E4ndert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.
eventUpdateTotpBodyHtml=<p>OTP wurde am {0} von {1} ge\u00E4ndert. Falls das nicht Sie waren, dann kontaktieren Sie bitte Ihren Admin.</p>

requiredAction.CONFIGURE_TOTP=Mehrfachauthentifizierung konfigurieren
requiredAction.terms_and_conditions=Bedingungen und Konditionen
requiredAction.UPDATE_PASSWORD=Passwort aktualisieren
requiredAction.UPDATE_PROFILE=Profil aktualisieren
requiredAction.VERIFY_EMAIL=E-Mail-Adresse verifizieren

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=Sekunden
linkExpirationFormatter.timePeriodUnit.seconds.1=Sekunde
linkExpirationFormatter.timePeriodUnit.minutes=Minuten
linkExpirationFormatter.timePeriodUnit.minutes.1=Minute
linkExpirationFormatter.timePeriodUnit.hours=Stunden
linkExpirationFormatter.timePeriodUnit.hours.1=Stunde
linkExpirationFormatter.timePeriodUnit.days=Tage
linkExpirationFormatter.timePeriodUnit.days.1=Tag

emailVerificationBodyCode=Bitte verifizieren Sie Ihre E-Mail-Adresse, indem Sie den folgenden Code eingeben.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Bitte verifizieren Sie Ihre E-Mail-Adresse, indem Sie den folgenden Code eingeben.</p><p><b>{0}</b></p>
