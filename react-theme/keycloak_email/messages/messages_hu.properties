# encoding: utf-8
emailVerificationSubject=Email cÃ­m megerÅsÃ­tÃ©se
emailVerificationBody=Ezzel az email cÃ­mmel valaki lÃ©trehozott egy {2} tartomÃ¡ny felhasznÃ¡lÃ³i fiÃ³kot. Amennyiben a fiÃ³kot Ãn hozta lÃ©tre, kÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy megerÅsÃ­tse fiÃ³kjÃ¡t Ã©s ezt az email cÃ­met.\n\n{0}\n\nA hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {3} mÃºlva.\n\nHa nem Ã¶n hozta lÃ©tre a felhasznÃ¡lÃ³i fiÃ³kot, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet.
emailVerificationBodyHtml=<p>Ezzel az email cÃ­mmel valaki lÃ©trehozott egy {2} tartomÃ¡ny felhasznÃ¡lÃ³i fiÃ³kot. Amennyiben a fiÃ³kot Ãn hozta lÃ©tre, kÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy megerÅsÃ­tse fiÃ³kjÃ¡t Ã©s ezt az email cÃ­met.</p><p><a href="{0}">HivatkozÃ¡s a fiÃ³k Ã©s az email cÃ­m megerÅsÃ­tÃ©sÃ©hez</a></p><p>A hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {3} mÃºlva.</p><p>Ha nem Ã¶n hozta lÃ©tre a felhasznÃ¡lÃ³i fiÃ³kot, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet.</p>
emailTestSubject=[KEYCLOAK] - SMTP teszt Ã¼zenet
emailTestBody=Ez egy KEYCLOAK teszt Ã¼zenet.
emailTestBodyHtml=<p>Ez egy KEYCLOAK teszt Ã¼zenet.</p>
identityProviderLinkSubject={0} Ã¶sszekÃ¶tÃ©s
identityProviderLinkBody=Valaki Ã¶ssze kÃ­vÃ¡nja kÃ¶tni az Ãn "{1}" tartomÃ¡nyi fiÃ³kjÃ¡t a(z) "{0}" szemÃ©lyazonossÃ¡g-kezelÅ {2} felhasznÃ¡lÃ³i fiÃ³kjÃ¡val. Amennyiben az Ã¶sszekÃ¶tÃ©st Ãn kezdemÃ©nyezte kÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy Ã¶sszekÃ¶sse fiÃ³kjait.\n\n{3}\n\nA hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {5} mÃºlva.\n\nHa nem Ã¶n kezdemÃ©nyezte a felhasznÃ¡lÃ³i fiÃ³kok Ã¶sszekÃ¶tÃ©sÃ©t, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet.\n\nHa Ã¶sszekÃ¶ti a fiÃ³kjait, akkor belÃ©phet a(z) {1} tartomÃ¡nyba a(z) {0} szolgÃ¡ltatÃ³n keresztÃ¼l.
identityProviderLinkBodyHtml=<p>Valaki Ã¶ssze kÃ­vÃ¡nja kÃ¶tni az Ãn <b>{1}</b> tartomÃ¡ny fiÃ³kjÃ¡t a(z) <b>{0}</b> szemÃ©lyazonossÃ¡g-kezelÅ {2} felhasznÃ¡lÃ³i fiÃ³kjÃ¡val. Amennyiben az Ã¶sszekÃ¶tÃ©st Ãn kezdemÃ©nyezte kÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy Ã¶sszekÃ¶sse fiÃ³kjait.</p><p><a href="{3}">HivatkozÃ¡s a fiÃ³k Ã¶sszekÃ¶tÃ©s megerÅsÃ­tÃ©shez</a></p><p>A hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {5} mÃºlva.</p><p>Ha nem Ã¶n kezdemÃ©nyezte a felhasznÃ¡lÃ³i fiÃ³kok Ã¶sszekÃ¶tÃ©sÃ©t, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet.</p><p>Ha Ã¶sszekÃ¶ti a fiÃ³kjait, akkor belÃ©phet a(z) {1} tartomÃ¡nyba a(z) {0} szolgÃ¡ltatÃ³n keresztÃ¼l.</p>
passwordResetSubject=JelszÃ³ visszaÃ¡llÃ­tÃ¡s
passwordResetBody=Valaki vissza kÃ­vÃ¡nja Ã¡llÃ­tani az Ãn "{2}" tartomÃ¡nyi fiÃ³kjÃ¡nak jelszavÃ¡t. Amennyiben a jelszÃ³ visszaÃ¡llÃ­tÃ¡st Ãn kezdemÃ©nyezte, kÃ©rem kattintson a lenti hivatkozÃ¡sra a jelszava megvÃ¡ltoztatÃ¡sÃ¡hoz.\n\n{0}\n\nA hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {3} mÃºlva.\n\nHa nem Ã¶n kÃ©rte a jelszÃ³ visszaÃ¡llÃ­tÃ¡st, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet, a jelszava nem fog megvÃ¡ltozni.
passwordResetBodyHtml=<p>Valaki vissza kÃ­vÃ¡nja Ã¡llÃ­tani az Ãn "{2}" tartomÃ¡nyi fiÃ³kjÃ¡nak jelszavÃ¡t. Amennyiben a jelszÃ³ visszaÃ¡llÃ­tÃ¡st Ãn kezdemÃ©nyezte, kÃ©rem kattintson a lenti hivatkozÃ¡sra a jelszava megvÃ¡ltoztatÃ¡sÃ¡hoz.</p><p><a href="{0}">HivatkozÃ¡s a jelszÃ³ visszaÃ¡llÃ­tÃ¡shoz</a></p><p>A hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {3} mÃºlva.</p><p>Ha nem Ã¶n kÃ©rte a jelszÃ³ visszaÃ¡llÃ­tÃ¡st, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet, a jelszava nem fog megvÃ¡ltozni.</p>
executeActionsSubject=FelhasznÃ¡lÃ³i fiÃ³k adatok mÃ³dosÃ­tÃ¡sa
executeActionsBody=Az alkalmazÃ¡s adminisztrÃ¡tora kezdemÃ©nyezte az Ãn "{2}" tartomÃ¡nyi felhasznÃ¡lÃ³i fiÃ³k adatainak mÃ³dosÃ­tÃ¡sÃ¡t a kÃ¶vetkezÅ mÅ±veletekkel: {3}. KÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy megkezdhesse a kÃ©rt mÃ³dosÃ­tÃ¡sokat.\n\n{0}\n\nA hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {4} mÃºlva.\n\nHa nincs tudomÃ¡sa arrÃ³l, hogy az adminisztrÃ¡tora mÃ³dosÃ­tÃ¡sokat kÃ©rt ÃntÅl, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet, az adatai nem fognak megvÃ¡ltozni.
executeActionsBodyHtml=<p>Az alkalmazÃ¡s adminisztrÃ¡tora kezdemÃ©nyezte az Ãn "{2}" tartomÃ¡nyi felhasznÃ¡lÃ³i fiÃ³k adatainak mÃ³dosÃ­tÃ¡sÃ¡t a kÃ¶vetkezÅ mÅ±veletekkel: {3}. KÃ©rem kattintson a lenti hivatkozÃ¡sra, hogy megkezdhesse a kÃ©rt mÃ³dosÃ­tÃ¡sokat.</p><p><a href="{0}">HivatkozÃ¡s a felhasznÃ¡lÃ³i fiÃ³k adatok mÃ³dosÃ­tÃ¡sÃ¡hoz</a></p><p>A hivatkozÃ¡s Ã©rvÃ©nyÃ©t veszti {4} mÃºlva.</p><p>Ha nincs tudomÃ¡sa arrÃ³l, hogy az adminisztrÃ¡tora mÃ³dosÃ­tÃ¡sokat kÃ©rt ÃntÅl, akkor kÃ©rem hagyja figyelmen kÃ­vÃ¼l ezt az Ã¼zenetet, az adatai nem fognak megvÃ¡ltozni.</p>
eventLoginErrorSubject=BelÃ©pÃ©si hiba
eventLoginErrorBody=Sikertelen belÃ©pÃ©si kÃ­sÃ©rlet tÃ¶rtÃ©nt {0} idÅpontban a(z) {1} cÃ­mrÅl. KÃ©rem lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n prÃ³bÃ¡lt meg belÃ©pni.
eventLoginErrorBodyHtml=<p>Sikertelen belÃ©pÃ©si kÃ­sÃ©rlet tÃ¶rtÃ©nt {0} idÅpontban a(z) {1} cÃ­mrÅl. KÃ©rem lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n prÃ³bÃ¡lt meg belÃ©pni.</p>
eventRemoveTotpSubject=Egyszer hasznÃ¡latos jelszÃ³ (OTP) eltÃ¡volÃ­tÃ¡sa
eventRemoveTotpBody=Az egyszer hasznÃ¡latos jelszÃ³ (OTP) funkciÃ³t {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben eltÃ¡volÃ­tottuk a fiÃ³kjÃ¡rÃ³l. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte az OTP eltÃ¡volÃ­tÃ¡sÃ¡t.
eventRemoveTotpBodyHtml=<p>Az egyszer hasznÃ¡latos jelszÃ³ (OTP) funkciÃ³t {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben eltÃ¡volÃ­tottuk a fiÃ³kjÃ¡rÃ³l. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte az OTP eltÃ¡volÃ­tÃ¡sÃ¡t.</p>
eventUpdatePasswordSubject=JelszÃ³ csere
eventUpdatePasswordBody=JelszavÃ¡t {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben lecserÃ©ltÃ¼k. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte a jelszÃ³ cserÃ©t.
eventUpdatePasswordBodyHtml=<p>JelszavÃ¡t {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben lecserÃ©ltÃ¼k. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte a jelszÃ³ cserÃ©t.</p>
eventUpdateTotpSubject=Egyszer hasznÃ¡latos jelszÃ³ (OTP) csere
eventUpdateTotpBody=Az egyszer hasznÃ¡latos jelszÃ³ (OTP) beÃ¡llÃ­tÃ¡sait {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben mÃ³dosÃ­tottuk a fiÃ³kjÃ¡n. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte az OTP beÃ¡llÃ­tÃ¡sok mÃ³dosÃ­tÃ¡sÃ¡t.
eventUpdateTotpBodyHtml=<p>Az egyszer hasznÃ¡latos jelszÃ³ (OTP) beÃ¡llÃ­tÃ¡sait {0} idÅpontban a(z) {1} cÃ­mrÅl Ã©rkezÅ kÃ©rÃ©s Ã©rtelmÃ©ben mÃ³dosÃ­tottuk a fiÃ³kjÃ¡n. KÃ©rem haladÃ©ktalanul lÃ©pjen kapcsolatba az alkalmazÃ¡s adminisztrÃ¡torral amennyiben nem Ã¶n igÃ©nyelte az OTP beÃ¡llÃ­tÃ¡sok mÃ³dosÃ­tÃ¡sÃ¡t.</p>

requiredAction.CONFIGURE_TOTP=Egyszer hasznÃ¡latos jelszÃ³ (OTP) beÃ¡llÃ­tÃ¡sa
requiredAction.terms_and_conditions=FelhasznÃ¡lÃ¡si feltÃ©telek
requiredAction.UPDATE_PASSWORD=JelszÃ³ csere
requiredAction.UPDATE_PROFILE=FiÃ³k adatok mÃ³dosÃ­tÃ¡sa
requiredAction.VERIFY_EMAIL=Email cÃ­m megerÅsÃ­tÃ©se

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=mÃ¡sodperc
linkExpirationFormatter.timePeriodUnit.seconds.1=mÃ¡sodperc
linkExpirationFormatter.timePeriodUnit.minutes=perc
linkExpirationFormatter.timePeriodUnit.minutes.1=perc
linkExpirationFormatter.timePeriodUnit.hours=Ã³ra
linkExpirationFormatter.timePeriodUnit.hours.1=Ã³ra
linkExpirationFormatter.timePeriodUnit.days=nap
linkExpirationFormatter.timePeriodUnit.days.1=nap

emailVerificationBodyCode=KÃ©rem erÅsÃ­tse meg az email cÃ­mÃ©t a kÃ¶vetkezÅ kÃ³d megadÃ¡sÃ¡val.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>KÃ©rem erÅsÃ­tse meg az email cÃ­mÃ©t a kÃ¶vetkezÅ kÃ³d megadÃ¡sÃ¡val.</p><p><b>{0}</b></p>
