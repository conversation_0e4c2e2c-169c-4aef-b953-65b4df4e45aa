emailVerificationSubject=Verifica l''email
emailVerificationBody=Qualcuno ha creato un account {2} con questo indirizzo email. Se sei stato tu, fai clic sul link seguente per verificare il tuo indirizzo email\n\n{0}\n\nQuesto link scadr\u00e0 in {3}.\n\nSe non sei stato tu a creare questo account, ignora questo messaggio.
emailVerificationBodyHtml=<p>Qualcuno ha creato un account {2} con questo indirizzo email. Se sei stato tu, fai clic sul link seguente per verificare il tuo indirizzo email</p><p><a href="{0}">Link per verificare l''indirizzo email</a></p><p>Questo link scadr\u00e0 in {3}.</p><p>Se non sei stato tu a creare questo account, ignora questo messaggio.</p>
emailTestSubject=[KEYCLOAK] - messaggio di test SMTP
emailTestBody=Questo \u00e8 un messaggio di test
emailTestBodyHtml=<p>Questo \u00e8 un messaggio di test</p>
identityProviderLinkSubject=Link {0}
identityProviderLinkBody=Qualcuno vuole associare il tuo account "{1}" con l''account "{0}" dell''utente {2}. Se sei stato tu, fai clic sul link seguente per associare gli account\n\n{3}\n\nQuesto link scadr\u00e0 in {5}.\n\nSe non vuoi associare l''account, ignora questo messaggio. Se associ gli account, potrai accedere a {1} attraverso {0}.
identityProviderLinkBodyHtml=<p>Qualcuno vuole associare il tuo account <b>{1}</b> con l''account <b>{0}</b> dell''utente {2}. Se sei stato tu, fai clic sul link seguente per associare gli account</p><p><a href="{3}">{3}</a></p><p>Questo link scadr\u00e0 in {5}.</p><p>Se non vuoi associare l''account, ignora questo messaggio. Se associ gli account, potrai accedere a {1} attraverso {0}.</p>
passwordResetSubject=Reimposta la password
passwordResetBody=Qualcuno ha appena richiesto di cambiare le credenziali di accesso al tuo account {2}. Se sei stato tu, fai clic sul link seguente per reimpostarle.\n\n{0}\n\nQuesto link e codice scadranno in {3}.\n\nSe non vuoi reimpostare le tue credenziali di accesso, ignora questo messaggio e non verr\u00e0 effettuato nessun cambio.
passwordResetBodyHtml=<p>Qualcuno ha appena richiesto di cambiare le credenziali di accesso al tuo account {2}. Se sei stato tu, fai clic sul link seguente per reimpostarle.</p><p><a href="{0}">{0}</a></p><p>Questo link scadr\u00e0 in {3}.</p><p>Se non vuoi reimpostare le tue credenziali di accesso, ignora questo messaggio e non verr\u00e0 effettuato nessun cambio.</p>
executeActionsSubject=Aggiorna il tuo account
executeActionsBody=Il tuo amministratore ha appena richiesto un aggiornamento del tuo account {2} ed \u00e8 necessario che tu esegua la/le seguente/i azione/i: {3}. Fai clic sul link seguente per iniziare questo processo.\n\n{0}\n\nQuesto link scadr\u00e0 in {4}.\n\nSe non sei a conoscenza della richiesta del tuo amministratore, ignora questo messaggio e non verr\u00e0 effettuato nessun cambio.
executeActionsBodyHtml=<p>Il tuo amministratore ha appena richiesto un aggiornamento del tuo account {2} ed \u00e8 necessario che tu esegua la/le seguente/i azione/i: {3}. Fai clic sul link seguente per iniziare questo processo.</p><p><a href="{0}">Link to account update</a></p><p>Questo link scadr\u00e0 in {4}.</p><p>Se non sei a conoscenza della richiesta del tuo amministratore, ignora questo messaggio e non verr\u00e0 effettuato nessun cambio.</p>
eventLoginErrorSubject=Errore di accesso
eventLoginErrorBody=\u00c8 stato rilevato un tentativo fallito di accesso al tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventLoginErrorBodyHtml=<p>\u00c8 stato rilevato un tentativo fallito di accesso al tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventRemoveTotpSubject=Rimozione OTP (password temporanea valida una volta sola)
eventRemoveTotpBody=La OTP (password temporanea valida una volta sola) \u00e8 stata rimossa dal tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventRemoveTotpBodyHtml=<p>La OTP (password temporanea valida una volta sola) \u00e8 stata rimossa dal tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventUpdatePasswordSubject=Aggiornamento password
eventUpdatePasswordBody=La tua password \u00e8 stata cambiata il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventUpdatePasswordBodyHtml=<p>La tua password \u00e8 stata cambiata il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>
eventUpdateTotpSubject=Aggiornamento OTP (password temporanea valida una volta sola)
eventUpdateTotpBody=La OTP (password temporanea valida una volta sola) \u00e8 stata aggiornata per il tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.
eventUpdateTotpBodyHtml=<p>La OTP (password temporanea valida una volta sola) \u00e8 stata aggiornata per il tuo account il {0} da {1}. Se non sei stato tu, per favore contatta l''amministratore.</p>

requiredAction.CONFIGURE_TOTP=Configurazione OTP
requiredAction.terms_and_conditions=Termini e condizioni
requiredAction.UPDATE_PASSWORD=Aggiornamento password
requiredAction.UPDATE_PROFILE=Aggiornamento profilo
requiredAction.VERIFY_EMAIL=Verifica dell''indirizzo email

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=secondi
linkExpirationFormatter.timePeriodUnit.seconds.1=secondo
linkExpirationFormatter.timePeriodUnit.minutes=minuti
linkExpirationFormatter.timePeriodUnit.minutes.1=minuto
#for language which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like this:
#linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=ore
linkExpirationFormatter.timePeriodUnit.hours.1=ora
linkExpirationFormatter.timePeriodUnit.days=giorni
linkExpirationFormatter.timePeriodUnit.days.1=giorno

emailVerificationBodyCode=Per favore verifica il tuo indirizzo email inserendo il codice seguente.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Per favore verifica il tuo indirizzo email inserendo il codice seguente.</p><p><b>{0}</b></p>
