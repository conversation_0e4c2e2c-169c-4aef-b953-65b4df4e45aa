emailVerificationSubject=V\u00e9rification du courriel
emailVerificationBody=Quelqu''un vient de cr\u00e9er un compte "{2}" avec votre courriel. Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous afin de v\u00e9rifier votre adresse de courriel\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message.
emailVerificationBodyHtml=<p>Quelqu''un vient de cr\u00e9er un compte "{2}" avec votre courriel. Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous afin de v\u00e9rifier votre adresse de courriel</p><p><a href="{0}">{0}</a></p><p>Ce lien expire dans {3}.</p><p>Sinon, veuillez ignorer ce message.</p>
emailUpdateConfirmationSubject=V\u00e9rification du nouveau courriel
emailUpdateConfirmationBody=Afin d''utiliser le courriel {1} dans votre compte {2}, cliquez sur le lien ci-dessous\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message.
emailUpdateConfirmationBodyHtml=<p>Afin d''utiliser le courriel {1} dans votre compte {2}, cliquez sur le lien ci-dessous</p><p><a href="{0}">{0}</a></p><p>Ce lien expirera dans {3}.</p><p>Sinon, veuillez ignorer ce message.</p>
identityProviderLinkSubject=Lien {0}
identityProviderLinkBody=Quelqu''un souhaite lier votre compte "{1}" au compte "{0}" de l''utilisateur {2} . Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous pour lier les comptes\n\n{3}\n\nCe lien expire dans {5}.\n\nSinon, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte. Si vous liez les comptes, vous pourrez vous connecter \u00e0 {1} via {0}.
identityProviderLinkBodyHtml=<p>Quelqu''un souhaite lier votre compte <b>{1}</b> au compte <b>{0}</b> de l''utilisateur {2}. Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous pour lier les comptes</p><p><a href="{3}">Lien pour confirmer la liaison des comptes</a></p><p>Ce lien expire dans {5}.</p><p>Sinon, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte. Si vous liez les comptes, vous pourrez vous connecter \u00e0 {1} via {0}.</p>
passwordResetSubject=R\u00e9initialiser le mot de passe
passwordResetBody=Quelqu''un vient de demander une r\u00e9initialisation de mot de passe pour votre compte {2}. Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous pour le mettre \u00e0 jour.\n\n{0}\n\nCe lien expire dans {3}.\n\nSinon, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte.
passwordResetBodyHtml=<p>Quelqu''un vient de demander une r\u00e9initialisation de mot de passe pour votre compte {2}. Si vous \u00eates \u00e0 l''origine de cette requ\u00eate, veuillez cliquer sur le lien ci-dessous pour le mettre \u00e0 jour.</p><p><a href="{0}">Lien pour r\u00e9initialiser votre mot de passe</a></p><p>Ce lien expire dans {3}.</p><p>Sinon, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte.</p>
executeActionsSubject=Mettre \u00e0 jour votre compte
executeActionsBody=Votre administrateur vient de demander une mise \u00e0 jour de votre compte {2} pour r\u00e9aliser les actions suivantes : {3}. Veuillez cliquer sur le lien ci-dessous afin de commencer le processus.\n\n{0}\n\nCe lien expire dans {4}.\n\nSi vous n''\u00eates pas \u00e0 l''origine de cette requ\u00eate, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte.
executeActionsBodyHtml=<p>Votre administrateur vient de demander une mise \u00e0 jour de votre compte {2} pour r\u00e9aliser les actions suivantes : {3}. Veuillez cliquer sur le lien ci-dessous afin de commencer le processus.</p><p><a href="{0}">{0}</a></p><p>Ce lien expire dans {4}.</p><p>Si vous n''\u00eates pas \u00e0 l''origine de cette requ\u00eate, veuillez ignorer ce message ; aucun changement ne sera effectu\u00e9 sur votre compte.</p>
eventLoginErrorSubject=Erreur de connexion
eventLoginErrorBody=Une tentative de connexion a \u00e9t\u00e9 d\u00e9tect\u00e9e sur votre compte {0} depuis {1}. Si vous n''\u00eates pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.
eventLoginErrorBodyHtml=<p>Une tentative de connexion a \u00e9t\u00e9 d\u00e9tect\u00e9e sur votre compte {0} depuis {1}. Si vous n''\u00eates pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.</p>
eventRemoveTotpSubject=Suppression du OTP
eventRemoveTotpBody=Le OTP a \u00e9t\u00e9 supprim\u00e9 de votre compte {0} depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.
eventRemoveTotpBodyHtml=<p>Le OTP a \u00e9t\u00e9 supprim\u00e9 de votre compte {0} depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.</p>
eventUpdatePasswordSubject=Mise \u00e0 jour du mot de passe
eventUpdatePasswordBody=Votre mot de passe pour votre compte {0} a \u00e9t\u00e9 modifi\u00e9 depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.
eventUpdatePasswordBodyHtml=<p>Votre mot de passe pour votre compte {0} a \u00e9t\u00e9 modifi\u00e9 depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.</p>
eventUpdateTotpSubject=Mise \u00e0 jour du OTP
eventUpdateTotpBody=Le OTP a \u00e9t\u00e9 mis \u00e0 jour pour votre compte {0} depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.
eventUpdateTotpBodyHtml=<p>Le OTP a \u00e9t\u00e9 mis \u00e0 jour pour votre compte {0} depuis {1}. Si vous n''\u00e9tiez pas \u00e0 l''origine de cette requ\u00eate, veuillez contacter votre administrateur.</p>

requiredAction.CONFIGURE_TOTP=Configurer un OTP
requiredAction.terms_and_conditions=Conditions g\u00e9n\u00e9rale d''utilisation
requiredAction.UPDATE_PASSWORD=Mise \u00e0 jour du mot de passe
requiredAction.UPDATE_PROFILE=Mise \u00e0 jour du profile
requiredAction.VERIFY_EMAIL=V\u00e9rification de l''adresse courriel

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=secondes
linkExpirationFormatter.timePeriodUnit.seconds.1=seconde
linkExpirationFormatter.timePeriodUnit.minutes=minutes
linkExpirationFormatter.timePeriodUnit.minutes.1=minute
linkExpirationFormatter.timePeriodUnit.hours=heures
linkExpirationFormatter.timePeriodUnit.hours.1=heure
linkExpirationFormatter.timePeriodUnit.days=jours
linkExpirationFormatter.timePeriodUnit.days.1=jour

emailVerificationBodyCode=Veuillez v\u00e9rifier votre adresse de courriel en saisissant le code suivant.\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>Veuillez v\u00e9rifier votre adresse de courriel en saisissant le code suivant.</p><p><b>{0}</b></p>
