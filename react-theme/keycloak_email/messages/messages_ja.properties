# encoding: utf-8
emailVerificationSubject=Eã¡ã¼ã«ã®ç¢ºèª
emailVerificationBody=ãã®ã¡ã¼ã«ã¢ãã¬ã¹ã§{2}ã¢ã«ã¦ã³ããä½æããã¾ãããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ã¡ã¼ã«ã¢ãã¬ã¹ã®ç¢ºèªãå®äºãã¦ãã ããã\n\n{0}\n\nãã®ãªã³ã¯ã¯{3}ã ãæå¹ã§ãã\n\nãããã®ã¢ã«ã¦ã³ãã®ä½æã«å¿å½ããããªãå ´åã¯ããã®ã¡ã¼ã«ãç¡è¦ãã¦ãã ããã
emailVerificationBodyHtml=<p>ãã®ã¡ã¼ã«ã¢ãã¬ã¹ã§{2}ã¢ã«ã¦ã³ããä½æããã¾ãããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ã¡ã¼ã«ã¢ãã¬ã¹ã®ç¢ºèªãå®äºãã¦ãã ããã</p><p><a href="{0}">ã¡ã¼ã«ã¢ãã¬ã¹ã®ç¢ºèª</a></p><p>ãã®ãªã³ã¯ã¯{3}ã ãæå¹ã§ãã</p><p>ãããã®ã¢ã«ã¦ã³ãã®ä½æã«å¿å½ããããªãå ´åã¯ããã®ã¡ã¼ã«ãç¡è¦ãã¦ãã ããã</p>
emailTestSubject=[KEYCLOAK] - SMTPãã¹ãã¡ãã»ã¼ã¸
emailTestBody=ããã¯ãã¹ãã¡ãã»ã¼ã¸ã§ã
emailTestBodyHtml=<p>ããã¯ãã¹ãã¡ãã»ã¼ã¸ã§ã</p>
identityProviderLinkSubject=ãªã³ã¯ {0}
identityProviderLinkBody=ããªãã®"{1}"ã¢ã«ã¦ã³ãã¨{2}ã¦ã¼ã¶ã¼ã®"{0}"ã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¦æ±ããã¾ãããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ã£ã¦ãã ããã\n\n{3}\n\nãã®ãªã³ã¯ã¯{5}ã ãæå¹ã§ãã\n\nããã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ããªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ããã¨ã§ã{0}çµç±ã§{1}ã«ã­ã°ã¤ã³ãããã¨ãã§ããããã«ãªãã¾ãã
identityProviderLinkBodyHtml=<p>ããªãã®<b>{1}</b>ã¢ã«ã¦ã³ãã¨{2}ã¦ã¼ã¶ã¼ã®<b>{0}</b>ã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¦æ±ããã¾ãããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ã£ã¦ãã ããã</p><p><a href="{3}">ã¢ã«ã¦ã³ããªã³ã¯ã®ç¢ºèª</a></p><p>ãã®ãªã³ã¯ã¯{5}ã ãæå¹ã§ãã</p><p>ããã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ããªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããã¢ã«ã¦ã³ãã®ãªã³ã¯ãè¡ããã¨ã§ã{0}çµç±ã§{1}ã«ã­ã°ã¤ã³ãããã¨ãã§ããããã«ãªãã¾ãã</p>
passwordResetSubject=ãã¹ã¯ã¼ãã®ãªã»ãã
passwordResetBody=ããªãã®{2}ã¢ã«ã¦ã³ãã®ãã¹ã¯ã¼ãã®å¤æ´ãè¦æ±ããã¦ãã¾ããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã¹ã¯ã¼ãã®ãªã»ãããè¡ã£ã¦ãã ããã\n\n{0}\n\nãã®ãªã³ã¯ã¯{3}ã ãæå¹ã§ãã\n\nãããã¹ã¯ã¼ãã®ãªã»ãããè¡ããªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããä½ãå¤æ´ããã¾ããã
passwordResetBodyHtml=<p>ããªãã®{2}ã¢ã«ã¦ã³ãã®ãã¹ã¯ã¼ãã®å¤æ´ãè¦æ±ããã¦ãã¾ããä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã¹ã¯ã¼ãã®ãªã»ãããè¡ã£ã¦ãã ããã</p><p><a href="{0}">ãã¹ã¯ã¼ãã®ãªã»ãã</a></p><p>ãã®ãªã³ã¯ã¯{3}ã ãæå¹ã§ãã</p><p>ãããã¹ã¯ã¼ãã®ãªã»ãããè¡ããªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããä½ãå¤æ´ããã¾ããã</p>
executeActionsSubject=ã¢ã«ã¦ã³ãã®æ´æ°
executeActionsBody=æ¬¡ã®ã¢ã¯ã·ã§ã³ãå®è¡ãããã¨ã«ãããç®¡çèããããªãã®{2}ã¢ã«ã¦ã³ãã®æ´æ°ãè¦æ±ããã¦ãã¾ã: {3}ãä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã®ãã­ã»ã¹ãéå§ãã¦ãã ããã\n\n{0}\n\nãã®ãªã³ã¯ã¯{4}ã ãæå¹ã§ãã\n\nç®¡çèããã®ãã®å¤æ´è¦æ±ã«ã¤ãã¦ãå­ç¥ãªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããä½ãå¤æ´ããã¾ããã
executeActionsBodyHtml=<p>æ¬¡ã®ã¢ã¯ã·ã§ã³ãå®è¡ãããã¨ã«ãããç®¡çèããããªãã®{2}ã¢ã«ã¦ã³ãã®æ´æ°ãè¦æ±ããã¦ãã¾ã: {3}ãä»¥ä¸ã®ãªã³ã¯ãã¯ãªãã¯ãã¦ãã®ãã­ã»ã¹ãéå§ãã¦ãã ããã</p><p><a href="{0}">ã¢ã«ã¦ã³ãã®æ´æ°</a></p><p>ãã®ãªã³ã¯ã¯{4}ã ãæå¹ã§ãã</p><p>ç®¡çèããã®ãã®å¤æ´è¦æ±ã«ã¤ãã¦ãå­ç¥ãªãå ´åã¯ããã®ã¡ãã»ã¼ã¸ãç¡è¦ãã¦ãã ãããä½ãå¤æ´ããã¾ããã</p>
eventLoginErrorSubject=ã­ã°ã¤ã³ã¨ã©ã¼
eventLoginErrorBody={0}ã«{1}ããã®ã­ã°ã¤ã³å¤±æãããªãã®ã¢ã«ã¦ã³ãã§æ¤åºããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã
eventLoginErrorBodyHtml=<p>{0}ã«{1}ããã®ã­ã°ã¤ã³å¤±æãããªãã®ã¢ã«ã¦ã³ãã§æ¤åºããã¾ãããå¿å½ããããªãå ´åã¯ç®¡çèã«é£çµ¡ãã¦ãã ããã</p>
eventRemoveTotpSubject=OTPã®åé¤
eventRemoveTotpBody={0}ã«{1}ããã®æä½ã§OTPãåé¤ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã
eventRemoveTotpBodyHtml=<p>{0}ã«{1}ããã®æä½ã§OTPãåé¤ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã</p>
eventUpdatePasswordSubject=ãã¹ã¯ã¼ãã®æ´æ°
eventUpdatePasswordBody={0}ã«{1}ããã®æä½ã§ããªãã®ãã¹ã¯ã¼ããå¤æ´ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã
eventUpdatePasswordBodyHtml=<p>{0}ã«{1}ããã®æä½ã§ããªãã®ãã¹ã¯ã¼ããå¤æ´ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã</p>
eventUpdateTotpSubject=OTPã®æ´æ°
eventUpdateTotpBody={0}ã«{1}ããã®æä½ã§OTPãæ´æ°ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã
eventUpdateTotpBodyHtml=<p>{0}ã«{1}ããã®æä½ã§OTPãæ´æ°ããã¾ãããå¿å½ããããªãå ´åã¯ãç®¡çèã«é£çµ¡ãã¦ãã ããã</p>

requiredAction.CONFIGURE_TOTP=OTPã®è¨­å®
requiredAction.terms_and_conditions=å©ç¨è¦ç´
requiredAction.UPDATE_PASSWORD=ãã¹ã¯ã¼ãã®æ´æ°
requiredAction.UPDATE_PROFILE=ãã­ãã¡ã¤ã«ã®æ´æ°
requiredAction.VERIFY_EMAIL=Eã¡ã¼ã«ã®ç¢ºèª

# units for link expiration timeout formatting
linkExpirationFormatter.timePeriodUnit.seconds=ç§
linkExpirationFormatter.timePeriodUnit.seconds.1=ç§
linkExpirationFormatter.timePeriodUnit.minutes=å
linkExpirationFormatter.timePeriodUnit.minutes.1=å
#for language which have more unit plural forms depending on the value (eg. Czech and other Slavic langs) you can override unit text for some other values like this: 
#linkExpirationFormatter.timePeriodUnit.minutes.2=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.3=minuty
#linkExpirationFormatter.timePeriodUnit.minutes.4=minuty
linkExpirationFormatter.timePeriodUnit.hours=æé
linkExpirationFormatter.timePeriodUnit.hours.1=æé
linkExpirationFormatter.timePeriodUnit.days=æ¥
linkExpirationFormatter.timePeriodUnit.days.1=æ¥

emailVerificationBodyCode=æ¬¡ã®ã³ã¼ããå¥åãã¦ã¡ã¼ã«ã¢ãã¬ã¹ãç¢ºèªãã¦ãã ããã\n\n{0}\n\n.
emailVerificationBodyCodeHtml=<p>æ¬¡ã®ã³ã¼ããå¥åãã¦ã¡ã¼ã«ã¢ãã¬ã¹ãç¢ºèªãã¦ãã ããã</p><p><b>{0}</b></p>

