import React, { FC } from 'react';
import { IconProps } from './IconProps'; // Adjust the path accordingly

const IconBackArrow: FC<IconProps> = ({width,height}) => {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 20 20.828">
      <g id="Icon" transform="translate(1 1.414)">
        <path id="Path" d="M18,.643H0" transform="translate(0 8.357)" fill="none" stroke="#26287a" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
        <path id="Path-2" data-name="Path" d="M9,18,0,9,9,0" fill="none" stroke="#26287a" strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} />
      </g>
    </svg>
  );
};

export default IconBackArrow;
