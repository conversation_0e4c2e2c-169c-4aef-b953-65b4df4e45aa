import { CSSProperties, FC, SVGAttributes } from "react";
import { IconProps } from "./IconProps";

const Icons8User: FC<IconProps & { style?: CSSProperties }> = ({
  height,
  width,
  style,
}) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={width}
      height={height}
      viewBox="0 0 15 15.683"
      style={style}
    >
      <path
        id="icons8-user_1_"
        data-name="icons8-user (1)"
        d="M11.5,3A4.091,4.091,0,0,0,7.409,7.091v.682a4.091,4.091,0,0,0,8.182,0V7.091A4.091,4.091,0,0,0,11.5,3Zm0,10.91c-2.731,0-6.236,1.477-7.245,2.789a1.238,1.238,0,0,0,.992,1.984H17.753a1.238,1.238,0,0,0,.992-1.984C17.736,15.388,14.23,13.91,11.5,13.91Z"
        transform="translate(-4 -3)"
      />
    </svg>
  );
};
export default Icons8User;
