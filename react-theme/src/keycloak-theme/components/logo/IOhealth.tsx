import { FC } from "react";
import { IconProps } from "./IconProps";

const IOHealthIcon:FC<IconProps> = ({width,height}) => {
    return (
        <svg xmlns="http://www.w3.org/2000/svg" width={width} height={height} viewBox="0 0 135.507 54.726">
            <defs>
                <radialGradient id="radial-gradient" cx="1.2" cy="-.8" r="3" gradientUnits="objectBoundingBox">
                    <stop offset="0" stop-color="#882558" />
                    <stop offset="1" stop-color="#25287f" />
                </radialGradient>
            </defs>
            <g id="Group_10657" data-name="Group 10657" transform="translate(-86.448 -217)">
                <g id="Group_10286" data-name="Group 10286" transform="translate(86.448 217)">
                    <path id="Path_12745" data-name="Path 12745" d="M34.727,54.139a42.864,42.864,0,0,1-18.744-1.061C9.742,51.194,3.3,47.573,1.067,42.207c-2.176-5.374-.162-12.46,2.665-18.5a47.961,47.961,0,0,1,11.225-15.6C19.828,3.532,26.128-.636,31.794.08c5.642.749,10.649,6.382,14.365,11.952a44.823,44.823,0,0,1,7.347,17.224c1.161,6.21,1,13.132-2.409,17.55-3.38,4.441-10.015,6.379-16.371,7.333" transform="translate(-0.045 0)" fill="#25287f" opacity="0.1" />
                    <path id="Path_12746" data-name="Path 12746" d="M26.344,54.717A41.446,41.446,0,0,1,9.761,47.329C4.764,43.485.18,38,0,32.384c-.124-5.6,4.127-11.321,8.755-15.812a46.375,46.375,0,0,1,15.5-10.251c5.974-2.471,13.1-4.078,17.977-1.487,4.845,2.612,7.44,9.423,8.889,15.733a43.362,43.362,0,0,1,.736,18.093c-1.081,6.013-3.6,12.217-8.2,15.041-4.58,2.856-11.244,2.332-17.317,1.016" transform="translate(0 -1.054)" fill="url(#radial-gradient)" />
                    <g id="Group_10264" data-name="Group 10264" transform="translate(16.121 18.652)">
                        <path id="Path_12753" data-name="Path 12753" d="M42.19,31.269A10.237,10.237,0,1,0,52.426,41.506,10.237,10.237,0,0,0,42.19,31.269m0,17.407a7.17,7.17,0,1,1,7.17-7.17,7.17,7.17,0,0,1-7.17,7.17" transform="translate(-25.589 -30.607)" fill="#fff" />
                        <path id="Path_12754" data-name="Path 12754" d="M26.252,30.442A2.178,2.178,0,0,1,28.5,32.587a2.254,2.254,0,0,1-4.5,0,2.162,2.162,0,0,1,2.251-2.145m0,20.251h0a1.87,1.87,0,0,1-1.914-1.823V38.091a1.916,1.916,0,0,1,3.827,0V48.87a1.87,1.87,0,0,1-1.914,1.823" transform="translate(-24.001 -30.442)" fill="#fff" />
                        <path id="Path_12755" data-name="Path 12755" d="M42.934,34.762a7.49,7.49,0,1,0,7.49,7.49,7.49,7.49,0,0,0-7.49-7.49m-.047,13.489A6.048,6.048,0,1,1,48.934,42.2a6.048,6.048,0,0,1-6.048,6.048" transform="translate(-26.285 -31.304)" fill="#fff" />
                    </g>
                </g>
                <text id="Health" transform="translate(181.956 252.758)" fill="#25287f" font-size="24" font-family="Poppins-SemiBold, Poppins" font-weight="600"><tspan x="-39" y="0">health</tspan></text>
            </g>
        </svg>

    );
};

export default IOHealthIcon;