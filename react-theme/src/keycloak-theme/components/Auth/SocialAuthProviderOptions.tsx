import * as React from 'react';
import Icons8UaePass from "../logo/Icons8UaePass";
import Icons8Apple from "../logo/Icons8Apple";
import Icons8Google from "../logo/Icons8Google";
import AuthOption from "./AuthOption";

interface SocialAuthProviderOptionsProps {
    hrefs?: string[];
}

const SocialAuthProviderOptions: React.FC<SocialAuthProviderOptionsProps> = ({ hrefs = ["#", "#", "#"] }) => {
    return (
        <section className="Auth-options">
            <div className="Auth-options__title">Sign in with</div>
            <div className="Auth-options__container">
                <AuthOption href={hrefs[0]} rightLogo={<Icons8UaePass width="32.5px" height="32.5px" />} title={"UAE PASS"} />
                <AuthOption href={hrefs[1]} rightLogo={<Icons8Google width="32.5px" height="32.5px" />} title={"GMAIL"} />
                <AuthOption href={hrefs[2]} rightLogo={<Icons8Apple width="32.5px" height="32.5px" />} title={"APPLE"} />
            </div>
        </section>
    );
}

export default SocialAuthProviderOptions;