import React from 'react';

interface AuthOptionProps {
    href: string;
    rightLogo: React.ReactNode;
    title: string;
}

const AuthOption: React.FC<AuthOptionProps> = ({
    href,
    rightLogo,
    title,
}: AuthOptionProps) => {
    return (
        <div className="Auth-option__item">
            <a role="button" href={href} className="Auth-option__item-link">
                <div className="Auth-option__item-logo">{rightLogo}</div>
                <div className="Auth-option__item-title">
                    {title}
                </div>
            </a>
        </div>
    );
}

export default AuthOption;
