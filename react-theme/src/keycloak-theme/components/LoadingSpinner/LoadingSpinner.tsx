import React, { CSSProperties } from "react";
import LoadingSpinnerIcon from "../logo/LoadingSpinner";
import { IconProps } from "../logo/IconProps";

export interface LoadingSpinnerProps {
  style?: CSSProperties;
  textStyles?: CSSProperties;
  label?: string;
  withLabel?: boolean;
  iconProps?: IconProps;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  withLabel,
  style,
  textStyles,
  iconProps,
  label = "Loading...",
}) => {
  return (
    <div style={{ padding: 20, ...style }}>
      <div>
        <LoadingSpinnerIcon width="50px" height="50px" {...iconProps} />
      </div>
      {withLabel ? (
        <div style={{ marginTop: 8, ...textStyles }}>{label}</div>
      ) : null}
    </div>
  );
};

export default LoadingSpinner;
