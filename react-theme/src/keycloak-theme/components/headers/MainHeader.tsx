import React from 'react';
import IOhealth from "../logo/IOhealth";
import { usePageContext } from "../../context/PageContext";

interface MainHeaderProps {
    routeToLogin?: boolean;
}

const MainHeader: React.FC<MainHeaderProps> = ({routeToLogin= true}) => {
    const { setPage, Routes } = usePageContext();

    return (
        <header id="header" className='header'>
            <div role="button" onClick={() => routeToLogin && setPage(Routes.sign_in)} className="header__logo">
                <span><IOhealth height="55px" width="133px" /></span>
            </div>
        </header>
    );
}

export default MainHeader;
