import React from 'react';
import { usePageContext } from "../../context/PageContext";
import IOHealthIcon from "../logo/IOhealth";
import IconBackArrow from "../logo/IconBackArrow";

interface BackWithLogoProps {
    pageCount?: number;
}

const BackWithLogo: React.FC<BackWithLogoProps> = (props) => {
    const { setBackPage } = usePageContext();

    return (
        <header className='header'>
            <button className="header__logo header-back__logo" tabIndex={0} onClick={() => setBackPage(props.pageCount || 1)}>
                <IconBackArrow height="18px" width="18px" />
            </button>
            <div className="header__logo mg-50">
                <span><IOHealthIcon height="42px" width='105px' /></span>
            </div>
        </header>
    );
}

export default BackWithLogo;