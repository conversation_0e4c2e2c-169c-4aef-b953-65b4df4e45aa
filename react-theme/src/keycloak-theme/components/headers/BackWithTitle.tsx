import React from 'react';
import IconBackArrow from "../logo/IconBackArrow";
import { usePageContext } from '../../context/PageContext';

interface BackWithTitleProps {
    title: string;
    pageCount?: number;
}

const BackWithTitle: React.FC<BackWithTitleProps> = (props) => {
    const { setBackPage } = usePageContext();
    return (
        <header className='header'>
            <button className="header__logo header-back__logo" tabIndex={0} onClick={() => setBackPage(props.pageCount || 1)}>
                <IconBackArrow height="18px" width="18px" />
            </button>
            <h1 className="header__title">{props.title}</h1>
        </header>
    );
}
export default BackWithTitle;
