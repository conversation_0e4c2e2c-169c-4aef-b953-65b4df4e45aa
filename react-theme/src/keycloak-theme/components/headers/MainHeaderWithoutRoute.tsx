import React from 'react';
import IOhealth from "../logo/IOhealth";

interface MainHeaderWithoutRouteProps {
    href: string;
}

const MainHeaderWithoutRoute: React.FC<MainHeaderWithoutRouteProps> = ({href}) => {
    return (
        <header className='header'>
            <a href={href || "#"} className="header__logo" >
                <span><IOhealth height="55px" width="133px"/></span>
            </a>
        </header>
    );
}

export default MainHeaderWithoutRoute;
