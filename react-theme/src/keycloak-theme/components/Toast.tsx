import React, { ReactElement } from "react";
import Close from "./logo/Close";

export interface ToastProps {
    classes?: {
        toast?: string;
        close?: string;
        body?: string;
        logo?: string;
        text?: string;
        toastBar?: "success-bar"|"warning-bar"|"info-bar"|"failed-bar";
    };
    elements?: {
        close_logo?: ReactElement;
        body_logo?: ReactElement;
        body_message?:  string;
    };
    attributes?: React.HTMLAttributes<HTMLDivElement>;
    behavior?: {
        onCloseClicked?: () => void;
    };
}

export const Toast = function ({
    classes = {},
    elements = {},
    attributes = {},
    behavior = { onCloseClicked: () => { } },
}: ToastProps) {
    return (
        <div {...attributes} className={`toast ${classes.toast}`}>
            <div onClick={behavior.onCloseClicked} className={`toast-close ${classes.close}`}>
                {elements.close_logo ? (
                    elements.close_logo
                ) : (
                    <Close width="12px" height="12px"/>
                )}
            </div>
            <div className={`toast__body ${classes.body}`}>
                <div className={`toast__logo ${classes.logo}`}>
                    {elements.body_logo}
                </div>
                <div className={`toast__text ${classes.text}`}>{elements.body_message ? elements.body_message : "change the body_message"}</div>
            </div>
            <div className={`toast-bar ${classes.toastBar}`}></div>
        </div>
    );
};
