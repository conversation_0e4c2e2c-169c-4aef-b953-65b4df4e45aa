import React, { useEffect, useState, ReactElement } from "react";
import { InputOTP } from "./input/InputOTP";
import useCountDown from "../hooks/useCountDown";

interface OtpFormProps {
  codeLength?: number;
  message?: string;
  onResend?: () => void;
  onSubmit: (otp: string) => void;
  outterError?: string;
  seconds: number;
  username: string;
}

export default function OtpForm(props: OtpFormProps): ReactElement {
  const {
    codeLength = 4,
    message = "",
    onResend: onResendClick,
    onSubmit,
    outterError,
    seconds,
  } = props;
  const {
    isDone: canResend,
    remainingSeconds,
    remainingMinutes,
    startCountDown,
  } = useCountDown(seconds);
  const [OTP, setOTP] = useState<string>("");
  const [error, setError] = useState<string>("");
  const [isButtonDisable, setIsButtonDisable] = useState(false);
  useEffect(() => {
    startCountDown();
  }, []);

  const handelResend = async function (): Promise<void> {
    if (!canResend) return;
    startCountDown();
    onResendClick?.();
  };

  const handleSubmit = async function (e: React.FormEvent): Promise<void> {
    setIsButtonDisable(true);
    e.preventDefault();
    let isError = false;
    if (OTP.length <= 3) isError = true;
    setError(isError ? "Invalid OTP" : "");
    setIsButtonDisable(false);
    if (isError) return;
    onSubmit(OTP);
  };

  return (
    <div className="Auth-operation-card">
      <form onSubmit={handleSubmit}>
        <div className="Auth-operation__title">Verification</div>
        <div className="Auth-operation__description">
          {message} <div className="phone">{props.username}</div>
        </div>
        <InputOTP
          onChange={(e) => {
            setOTP(e);
          }}
          numInputs={codeLength}
        />
        <div className="Auth-operation__action">
          Didn't receive an OTP?{" "}
          <span
            role="button"
            onClick={handelResend}
            className={`${!canResend ? "auth__link--disable" : "auth__link"
              } decoration `}
          >
            Resend {remainingMinutes}:{remainingSeconds}
          </span>
        </div>
        <div className="">
          <button
            type="submit"
            className={`Auth-card__button ${isButtonDisable ? "Auth-card__button--disable" : ""
              }`}
          >
            Verify
          </button>
        </div>
        {outterError ? (
          <div className="error-message error-login">{outterError}</div>
        ) : (
          ""
        )}
        {error ? <div className="error-message error-login">{error}</div> : ""}
      </form>
    </div>
  );
}

OtpForm.defaultProps = {
  codeLength: 4,
  message: "Please enter the 4-digit verification code that was sent to Your",
  onSubmit: () => { },
};
