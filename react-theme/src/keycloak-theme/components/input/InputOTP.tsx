import React, { useEffect, useRef, useState } from "react";
import { InputText } from "./InputText";

interface InputOTPProps {
    numInputs: number;
    onChange: (value: string) => void;
}

export function InputOTP({ numInputs, onChange }: InputOTPProps) {
    const [inputs, setInputs] = useState<string[]>(Array(numInputs).fill(""));
    const inputRef = useRef<HTMLInputElement[]>(Array(numInputs).fill(null));

    useEffect(() => {
        onChange(inputs.join(""));
    }, [inputs, onChange]);

    const handleInputChange = (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
        let arr = [...inputs];
        let input = e.target.value;

        if (index === 0 && input.length === numInputs && numInputs > 2) {
            const arr = input.split("");
            setInputs([...arr]);
            inputRef.current[input.length - 1].focus();
            setTimeout(() => {
                inputRef.current[input.length - 1].select();
            }, 0);
            return;
        }
        if (input.length >= 1) input = input[input.length - 1];
        if (!((input >= "0" && input <= "9") || input === "")) return;

        arr[index] = input;
        arr = arr.join("").split("");
        let arr_before_length = arr.length;

        while (arr.length < numInputs) arr.push("");

        if (input !== "") {
            if (arr_before_length < index + 1) {
                inputRef.current[arr_before_length].focus();
                setTimeout(() => {
                    inputRef.current[arr_before_length].select();
                }, 0);
            } else {
                let focus_index = index + 1 < numInputs ? index + 1 : index;
                inputRef.current[focus_index].focus();
                setTimeout(() => {
                    inputRef.current[focus_index].select();
                }, 0);
            }
        } else {
            inputRef.current[index].focus();
            setTimeout(() => {
                inputRef.current[index].select();
            }, 0);
        }

        setInputs(arr);
    };

    const handelkeyDown = (index: number) => (e: React.KeyboardEvent<HTMLInputElement>) => {
        if (e.keyCode === 37) {
            let focus_index = index - 1 >= 0 ? index - 1 : index;
            inputRef.current[focus_index].focus();
            setTimeout(() => {
                inputRef.current[focus_index].select();
            }, 0);
        } else if (e.keyCode === 39) {
            let focus_index = index + 1 < numInputs ? index + 1 : index;
            inputRef.current[focus_index].focus();
            inputRef.current[focus_index].select();
            setTimeout(() => {
                inputRef.current[focus_index].select();
            }, 0);
        } else if (e.keyCode === 8) {
            if (inputs[index] === "") {
                let focus_index = index - 1 >= 0 ? index - 1 : index;
                inputRef.current[focus_index].focus();
                setTimeout(() => {
                    inputRef.current[focus_index].select();
                }, 0);
            }
        }
    };

    return (
        <div className="input-row otp-row">
            {inputs.map((input, index) => (
                <InputText
                    inputProps={{
                        key: index,
                        ref: (element: any) => (inputRef.current[index] = element),
                        value: inputs[index],
                        onChange: handleInputChange(index),
                        onKeyDown: handelkeyDown(index),
                    }}
                    classes={{
                        input_classes: "otp-input",
                        input_component_Wrapper_classes: "OTP-wrapper",
                    }}
                />
            ))}
        </div>
    );
}
