import React, { ReactElement, InputHTMLAttributes, HTMLAttributes } from 'react';

interface InputRadioProps {
  inputProps: InputHTMLAttributes<HTMLInputElement>;
  classes: {
    input_Wrapper_classes?: string;
    input_classes?: string;
  };
  wrapperProps: HTMLAttributes<HTMLDivElement>;
}

const InputRadio: React.FC<InputRadioProps> = ({ inputProps, classes, wrapperProps }): ReactElement => {
  return (
    <div {...wrapperProps} className={`Input-Wrapper ${classes.input_Wrapper_classes}`}>
      <input type="radio" {...inputProps} className={`radio ${classes.input_classes}`} />
      <label htmlFor={inputProps.id} className="radio-label">
        {inputProps.value}
      </label>
    </div>
  );
}

export default InputRadio;