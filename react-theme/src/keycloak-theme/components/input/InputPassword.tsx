import React, { useState } from "react";
import { InputText} from "./InputText";
import InputElementProps from "./inputTypes";


export function InputPassword({ inputProps = {}, classes = {}, elements = {} }: InputElementProps) {
  const [showPass, setShowPass] = useState(false);

  return (
    <InputText
      inputProps={{
        ...inputProps,
        type: showPass ? "text" : "password",
      }}
      classes={{
        ...classes,
        input_leftLogo_classes: classes.input_leftLogo_classes + (showPass ? " hide-icon" : ""),
      }}
      elements={{
        ...elements,
        leftLogo: (
          <div role="button" onClick={() => setShowPass(prev => !prev)}>
            {showPass ? elements.hideLogo : elements.showLogo}
          </div>
        ),
      }}
    />
  );
}
