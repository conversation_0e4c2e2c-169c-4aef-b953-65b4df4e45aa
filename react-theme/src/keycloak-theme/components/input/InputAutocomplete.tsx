import React, { useState, useEffect } from "react";
import TextField from "@mui/material/TextField";
import Autocomplete from "@mui/material/Autocomplete";
import CircularProgress from "@mui/material/CircularProgress";
import Icons8User from "../logo/Icons8User";
import { useDebounce } from "keycloak-theme/hooks/useDebounce";

export interface Option {
  label: string;
  value: string;
}

interface InputAutocompleteProps {
  placeholder: string;
  noOptionsText?: string;
  options: Array<Option>;
  isLoading: boolean;
  hasError: boolean;
  onSearch: (searchTerm: string) => void;
  onSelect: (option: Option | null) => void;
}

const InputAutocomplete: React.FC<InputAutocompleteProps> = ({
  placeholder,
  noOptionsText,
  options,
  isLoading,
  hasError,
  onSearch,
  onSelect,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  const debouncedSearchTerm = useDebounce<string>(searchTerm, 700);

  const handleSelectionChange = (
    _: React.SyntheticEvent<Element, Event>,
    value: Option | null
  ) => {
    onSelect(value);
  };

  const handleInputChange = (
    _: React.SyntheticEvent<Element, Event>,
    value: string
  ) => {
    setSearchTerm(value);
  };

  useEffect(() => {
    if (searchTerm?.length > 2) {
      onSearch(searchTerm);
    }
  }, [debouncedSearchTerm]);

  return (
    <Autocomplete
      id="input-autocomplete"
      open={isOpen}
      onOpen={() => setIsOpen(true)}
      onClose={() => setIsOpen(false)}
      getOptionLabel={(option: Option) => option.label}
      options={options}
      loading={isLoading}
      onChange={handleSelectionChange}
      onInputChange={handleInputChange}
      noOptionsText={noOptionsText}
      classes={{
        noOptions: "autocomplete-no-options",
        option: "autocomplete-option",
      }}
      renderInput={(params) => (
        <TextField
          {...params}
          hiddenLabel
          error={hasError}
          placeholder={placeholder}
          inputProps={{
            ...params.inputProps,
            style: {
              paddingLeft: 8,
              paddingTop: 12,
              paddingBottom: 12,
              backgroundColor: "#F2F2FF",
            },
          }}
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {isLoading ? (
                  <CircularProgress color="inherit" size={20} />
                ) : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
            style: {
              paddingLeft: 20,
              fontFamily: "var(--main-Medium)",
              marginBottom: 15,
              height: 50,
              backgroundColor: "#F2F2FF",
              borderRadius: 28,
              color: "var(--main-color)",
              textIndent: 41,
            },
            startAdornment: <Icons8User width="15px" height="15.683px" />,
          }}
        />
      )}
    />
  );
};

export default InputAutocomplete;
