import React, { <PERSON> } from 'react';
import { TextAreaElementProps } from './inputTypes';

export const InputTextArea: FC<TextAreaElementProps> = ({ inputProps = {}, classes = {}, elements = {} }) => (
    <div className={`Input-component-Wrapper ${classes.input_component_Wrapper_classes ?? ""}`}>
        {elements.inputLabel}
        <div className={`Input-Wrapper ${classes.input_Wrapper_classes}`}>
            <textarea
                {...inputProps}
                className={`textAreaInput ${(classes.input_classes) ?? ""}`}
            />
            <div className={`custom-logo input__right-logo ${classes.input_rightLogo_classes ?? ""}`}>
                {elements.rightLogo}
            </div>
            <div className={`input__left-logo ${classes.input_leftLogo_classes ?? ""}`}>
                {elements.leftLogo}
            </div>
        </div>
        {elements.inputMessage}
    </div>
);

