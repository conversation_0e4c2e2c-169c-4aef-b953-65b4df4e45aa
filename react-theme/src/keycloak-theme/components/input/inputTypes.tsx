import React, { InputHTMLAttributes, ReactNode, TextareaHTMLAttributes } from "react";
export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
    key?: any;
    ref?: any;
    autocomplete?: any;
}
export interface Classes {
    [key: string]: string;
}

export interface Elements {
    [key: string]: ReactNode;
}

export default interface InputElementProps {
    elements?: Elements;
    classes?: Classes;
    inputProps: InputProps;
}

export interface TextAreaElementProps extends Omit<InputElementProps, "inputProps"> 
{
    inputProps: TextareaHTMLAttributes<HTMLTextAreaElement>;
}
