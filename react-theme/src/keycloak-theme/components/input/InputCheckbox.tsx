import React, { ReactNode, InputHTMLAttributes } from "react";
import { InputProps } from './inputTypes';

interface InputCheckboxProps {
    inputProps?:InputProps,
    classes?: {
        input_Wrapper_classes?: string;
        input_classes?: string;
        checkbox?: string;
    };
    wrapperProps?: React.HTMLAttributes<HTMLDivElement>;
    elements: {
        label: string;
        clickablePart?: ReactNode;
    };
}

export default function InputCheckbox({ 
    inputProps = {}, 
    classes = {}, 
    wrapperProps = {},
    elements={label:""},
}: InputCheckboxProps) {
    return (
        <div {...wrapperProps} className={`Input-Wrapper ${classes.input_Wrapper_classes}`}>
            <input 
                className={`${classes.input_classes}`}
                type="checkbox"
                {...inputProps}
            />
            <label htmlFor={inputProps.id} className={`${classes.checkbox}`}>
                <p>
                    {elements.label}
                    {elements.clickablePart}
                </p>
            </label>
        </div>
    );
}
