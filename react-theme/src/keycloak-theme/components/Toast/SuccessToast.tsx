import React, { ReactElement } from "react";
import { Toast, ToastProps } from "../Toast";
import Success from "../logo/Success";

export const SuccessToast = function ({
    attributes = {},
    elements = {},
    classes = {},
    behavior = {},
}: ToastProps): ReactElement {
    return (
        <Toast
            attributes={attributes}
            elements={{
                body_logo: (
                    <Success width="76.965px" height="76px"/>
                ),
                ...elements,
            }}
            classes={{
                toastBar: "success-bar",
                text:"success",
                ...classes,
            }}
            behavior={behavior}
        />
    )
}

