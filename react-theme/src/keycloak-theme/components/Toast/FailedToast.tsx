import React, { ReactElement } from "react";
import { Toast, ToastProps } from "../Toast";
import Failed from "../logo/Failed";



export const FailedToast = function ({
    attributes = {},
    elements = {},
    classes = {},
    behavior = {},
}: ToastProps): ReactElement {
    return (
        <Toast
            attributes={attributes}
            elements={{
                body_logo: (
                    <Failed width="76.965px" height="76px"/>
                ),
                ...elements,
            }}
            classes={{
                toastBar: "failed-bar",
                text:"failed",
                ...classes,
            }}
            behavior={behavior}
        />
    );
};
