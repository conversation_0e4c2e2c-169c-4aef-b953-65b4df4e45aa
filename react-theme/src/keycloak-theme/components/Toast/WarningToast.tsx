import React, { ReactElement } from "react";
import { Toast, ToastProps } from "../Toast";
import Warning from "../logo/Warning";

export const WarningToast = function ({
    attributes = {},
    elements = {},
    classes = {},
    behavior = {},
}: ToastProps): ReactElement {
    return (
        <Toast
            attributes={attributes}
            elements={{
                body_logo: (
                    <Warning width="76px" height="76.965px"/>
                ),
                ...elements,
            }}
            classes={{
                toastBar: "warning-bar",
                text:"warning",
                ...classes,
            }}
            behavior={behavior}
        />
    )
}

