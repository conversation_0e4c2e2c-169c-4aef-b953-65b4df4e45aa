import React, { ReactElement } from "react";
import { Toast, ToastProps } from "../Toast";
import InfoIcon from "../logo/InfoIcon";



export const InfoToast = function ({
    attributes = {},
    elements = {},
    classes = {},
    behavior = {},
}: ToastProps): ReactElement {
    return (
        <Toast
            attributes={attributes}
            elements={{
                body_logo: (
                    <InfoIcon height="76px" width="76.965px"/>
                ),
                ...elements,
            }}
            classes={{
                toastBar: "info-bar",
                text:"info",
                ...classes,
            }}
            behavior={behavior}
        />
    );
};
