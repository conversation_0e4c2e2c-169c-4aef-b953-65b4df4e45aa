import { gql } from "@apollo/client";

export const REQUEST_PASSWORD_RESET = gql`
  mutation RequestPasswordReset(
    $appType: AppTypes!
    $passwordResetMethod: PasswordResetMethodEnum!
    $username: String!
    $vendor: ID
  ) {
    requestPasswordReset(
      appType: $appType
      passwordResetMethod: $passwordResetMethod
      username: $username
      vendor: $vendor
    ) {
      sessionToken
      accountErrors {
        message
        field
        code
      }
    }
  }
`;

export const CONFIRM_PASSWORD_REST_OTP = gql`
  mutation ConfirmPasswordRestOtp(
    $appType: AppTypes!
    $passwordResetMethod: PasswordResetMethodEnum!
    $sessionToken: String!
    $username: String!
    $verificationCode: String!
    $vendor: ID
  ) {
    confirmPasswordRestOtp(
      appType: $appType
      passwordResetMethod: $passwordResetMethod
      sessionToken: $sessionToken
      username: $username
      verificationCode: $verificationCode
      vendor: $vendor
    ) {
      success
      accountErrors {
        message
        field
        code
      }
    }
  }
`;

export const SET_PASSWORD = gql`
  mutation SetPassword(
    $appType: AppTypes!
    $password: String!
    $passwordResetMethod: PasswordResetMethodEnum!
    $token: String!
    $username: String!
    $vendor: ID
  ) {
    setPassword(
      appType: $appType
      password: $password
      passwordResetMethod: $passwordResetMethod
      token: $token
      username: $username
      vendor: $vendor
    ) {
      success
      accountErrors {
        message
        field
        code
      }
    }
  }
`;

export const CHECK_CUSTOMER_ACCOUNT_EXIST = gql`
  mutation CheckCustomerAccountExist($contactNumber: String, $email: String) {
    checkCustomerAccountExist(contactNumber: $contactNumber, email: $email) {
      isContactNumberExists
      isEmailExists
      accountErrors {
        message
      }
    }
  }
`;

export const VERIFY_CREDENTIALS = gql`
  mutation VerifyCredentials($phoneNumber: String, $email: String) {
    verifyCredentials(phoneNumber: $phoneNumber, email: $email) {
      sessionToken
      accountErrors {
        message
      }
    }
  }
`;
export const REQUEST_OTP_FOR_DELETE_ACCOUNT = gql`
  mutation requestOtpForDeleteAccount($phoneNumber: String, $email: String) {
    requestOtpForDeleteAccount(phoneNumber: $phoneNumber, email: $email) {
      sessionToken
      accountErrors {
        message
      }
    }
  }
`;

export const CONFIRM_VERIFICATION = gql`
  mutation ConfirmVerification(
    $sessionToken: String!
    $verificationCode: String!
    $email: String
    $phoneNumber: String
  ) {
    confirmVerification(
      sessionToken: $sessionToken
      verificationCode: $verificationCode
      email: $email
      phoneNumber: $phoneNumber
    ) {
      accountErrors {
        message
      }
      success
    }
  }
`;

export const CUSTOMER_CREATE = gql`
  mutation CustomerCreate($input: CustomerCreateInput!) {
    customerCreate(input: $input) {
      patientErrors {
        message
      }
    }
  }
`;

export const DELETE_CUSTOMER_BY_EMAIL = gql`
  mutation customerDelete(
    $identity: String!
    $sessionToken: String!
    $reason: String!
  ) {
    customerDelete(
      input: {
        email: $identity
        deleteReason: $reason
        emailVerificationToken: $sessionToken
      }
    ) {
      accountErrors {
        code
        message
        field
      }
    }
  }
`;

export const DELETE_CUSTOMER_BY_MOBILE = gql`
  mutation customerDelete(
    $identity: String!
    $sessionToken: String!
    $reason: String!
  ) {
    customerDelete(
      input: {
        mobile: $identity
        deleteReason: $reason
        mobileVerificationToken: $sessionToken
      }
    ) {
      accountErrors {
        code
        message
        field
      }
    }
  }
`;
