import { gql } from "@apollo/client";

export const FETCH_VENDORS = gql`
  query Vendors(
    $first: Int
    $last: Int
    $after: String
    $before: String
    $filter: VendorFilterInput
  ) {
    vendors(
      first: $first
      last: $last
      after: $after
      before: $before
      filter: $filter
    ) {
      totalCount
      pageInfo {
        hasNextPage
        hasPreviousPage
        startCursor
        endCursor
      }
      edges {
        node {
          id
          name
        }
      }
    }
  }
`;
