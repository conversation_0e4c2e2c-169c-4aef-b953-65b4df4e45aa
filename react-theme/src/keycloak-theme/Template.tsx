// Copy pasted from: https://github.com/InseeFrLab/keycloakify/blob/main/src/lib/components/shared/Template.tsx

// You can replace all relative imports by cherry picking files from the keycloakify module.
// For example, the following import:
// import { assert } from "./tools/assert";
// becomes:
import { assert } from "keycloakify/lib/tools/assert";
import { clsx } from "keycloakify/lib/tools/clsx";
import type { TemplateProps } from "keycloakify/lib/KcProps";
import { usePrepareTemplate } from "keycloakify/lib/Template";
import type { KcContext } from "./kcContext";
import type { I18n } from "./i18n";
import React from "react";
import { Typography, useTheme } from "@mui/material";
import { green, primary } from "./pages/shared/theme/colors";
import { Box } from "@mui/system";
import MainHeaderWithoutRoute from "./components/headers/MainHeaderWithoutRoute";

export default function Template(props: TemplateProps<KcContext, I18n>) {
  const {
    displayInfo = false,
    displayMessage = true,
    displayRequiredFields = false,
    displayWide = false,
    showAnotherWayIfPresent = true,
    headerNode,
    showUsernameNode = null,
    formNode,
    infoNode = null,
    kcContext,
    i18n,
    doFetchDefaultThemeResources,
    stylesCommon,
    styles,
    scripts,
    kcHtmlClass,
  } = props;

  const { msg, changeLocale, labelBySupportedLanguageTag, currentLanguageTag } =
    i18n;

  const { realm, locale, auth, url, message, isAppInitiatedAction } = kcContext;

  const { isReady } = usePrepareTemplate({
    doFetchDefaultThemeResources,
    stylesCommon,
    styles,
    scripts,
    url,
    kcHtmlClass,
  });
  const { typography } = useTheme();

  if (!isReady) {
    return null;
  }

  return (
    <div className={clsx(props.kcLoginClass)}>
      <div
        className={clsx(
          props.kcFormCardClass,
          displayWide && props.kcFormCardAccountClass
        )}
      >
        <MainHeaderWithoutRoute href="#"/>
        <div className='app__container'>
        <div id="kc-content">
          <div id="kc-content-wrapper">
            {/* App-initiated actions should not see warning messages about the need to complete the action during login. */}

            {formNode}
            {auth !== undefined &&
              auth.showTryAnotherWayLink &&
              showAnotherWayIfPresent && (
                <form
                  id="kc-select-try-another-way-form"
                  action={url.loginAction}
                  method="post"
                  className={clsx(displayWide && props.kcContentWrapperClass)}
                >
                  <div
                    className={clsx(
                      displayWide && [
                        props.kcFormSocialAccountContentClass,
                        props.kcFormSocialAccountClass,
                      ]
                    )}
                  >
                    <div className={clsx(props.kcFormGroupClass)}>
                      <input type="hidden" name="tryAnotherWay" value="on" />
                      {/* eslint-disable-next-line jsx-a11y/anchor-is-valid */}
                      <a
                        href="#"
                        id="try-another-way"
                        onClick={() => {
                          document.forms[
                            "kc-select-try-another-way-form" as never
                          ].submit();
                          return false;
                        }}
                      >
                        {msg("doTryAnotherWay")}
                      </a>
                    </div>
                  </div>
                </form>
              )}
            {displayInfo && (
              <div id="kc-info" className={clsx(props.kcSignUpClass)}>
                <div
                  id="kc-info-wrapper"
                  className={clsx(props.kcInfoAreaWrapperClass)}
                >
                  {infoNode}
                </div>
              </div>
            )}
          </div>
        </div>
        </div>
      </div>
    </div>
  );
}
