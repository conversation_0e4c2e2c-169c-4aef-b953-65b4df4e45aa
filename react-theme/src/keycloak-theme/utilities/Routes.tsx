import React from "react";
import MainHeader from "../components/headers/MainHeader";
import SignIn from "../pages/SignIn";
import SignUp from "../pages/SignUp";
import BackWithTitle from "../components/headers/BackWithTitle";
import CustomTerms from "../pages/CustomTerms";
import Verification from "../pages/Verification";
import ResetPass from "../pages/ResetPass";
import CreateNewPass from "../pages/CreateNewPass";
import BackWithLogo from "../components/headers/BackWithLogo";
import SignupVerification from "../pages/SignupVerification";
import { defaultPropsForRoute } from "../pages/SignupVerification";
import DeleteAccountVerification from "keycloak-theme/pages/DeleteAccountVerification";
import DeleteAccount from "keycloak-theme/pages/deleteAccount";

export const Routes = {
  sign_in: ({ headerProps = {}, bodyProps = {} }) => {
    return (
      <>
        <MainHeader {...headerProps} />
        <div className="app__container">
          <SignIn {...headerProps} />
        </div>
      </>
    );
  },
  sign_up: ({ headerProps = {}, bodyProps = {} }) => (
    <>
      <MainHeader {...headerProps} />
      <div className="app__container">
        <SignUp {...bodyProps} />
      </div>
    </>
  ),
  terms: ({ headerProps = {}, bodyProps = {} }) => (
    <>
      <BackWithTitle {...{ title: "Terms of Use", ...headerProps }} />
      <div className="app__container">
        <CustomTerms {...bodyProps} />
      </div>
    </>
  ),
  verification: ({ headerProps = { pageCount: 2 }, bodyProps = {} }) => (
    <>
      <BackWithLogo {...headerProps} />
      <div className="app__container flex">
        <Verification {...bodyProps} />
      </div>
    </>
  ),
  reset_pass: ({ headerProps = {}, bodyProps = {} }) => (
    <>
      <BackWithLogo {...headerProps} />
      <div className="app__container flex">
        <ResetPass {...bodyProps} />
      </div>
    </>
  ),
  create_new_pass: ({ headerProps = { pageCount: 3 }, bodyProps = {} }) => (
    <>
      <BackWithLogo {...headerProps} />
      <div className="app__container flex">
        <CreateNewPass {...bodyProps} />
      </div>
    </>
  ),
  verify_credentials: ({ headerProps = { pageCount: 2 }, bodyProps = {} }) => (
    <>
      <BackWithLogo {...headerProps} />
      <div className="app__container flex">
        <SignupVerification {...defaultPropsForRoute} {...bodyProps} />
      </div>
    </>
  ),
  delete_account: ({ headerProps = { pageCount: 1 }, bodyProps = {} }) => (
    <>
      <div className="app__container flex">
        <DeleteAccount {...bodyProps} />
      </div>
    </>
  ),
  delete_account_verification: ({
    headerProps = { pageCount: 2 },
    bodyProps = {
      reason: "",
      identity: "",
      identityType: "mobile",
      sessionToken: "",
    },
  }) => (
    <>
      <BackWithLogo {...headerProps} />
      <div className="app__container flex">
        <DeleteAccountVerification {...bodyProps} />
      </div>
    </>
  ),
};
