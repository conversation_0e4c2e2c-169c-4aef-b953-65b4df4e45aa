import { FetchResult } from "@apollo/client";
import { GENERAL_FORM_ERROR_MSG } from "keycloak-theme/constants";
import { AccountError } from "keycloak-theme/types";

export const extractAccountErrorMessage = (response: FetchResult) => {
  return (
    response?.data?.requestPasswordReset?.accountErrors
      ?.map((error: AccountError) => error?.message)
      .join(", ") ?? GENERAL_FORM_ERROR_MSG
  );
};
