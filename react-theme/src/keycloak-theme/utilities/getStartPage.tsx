import { User, config } from "ApplicationConfig";
import { Routes } from "./Routes";

const getStartPage = () => {
  const url = new URL(document.URL);
  const urlParams = new URLSearchParams(url.search);
  const pageName = urlParams.get("go_to_page")?.toLowerCase();
  const clientId = urlParams.get("client_id")?.toLowerCase() || "any";
  const user: User =
    config.users.find((e) => e.client_id_in_URL === clientId) ||
    config.users[config.users.length - 1];
  const startPageName =
    (pageName && (getUserPages(user)[pageName] as keyof typeof Routes)) ||
    "sign_in";
  return Routes[startPageName];
};
const getUserPages = (user: User) => {
  const startPages: any = {
    sign_up: "sign_up",
    reset_pass: "reset_pass",

    // delete_account_root: "delete_account_container",
    delete_account: "delete_account",
    deleteAccountVerification: "deleteAccountVerification",
  };
  if (!user.display_signup) delete startPages.sign_up;
  if (!user.display_forget_password) delete startPages.reset_pass;
  return startPages;
};
export default getStartPage;
