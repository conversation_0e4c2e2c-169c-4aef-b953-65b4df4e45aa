import React, { createContext, useContext } from "react";

// Define the type for your context data
type LoginContextType = {
    kcContext: any; // Replace 'any' with the actual type
    i18n: any; // Replace 'any' with the actual type
    user:any,
};

export const LoginContext = createContext<LoginContextType | undefined>(undefined);

export function useLoginContext() {
    const context = useContext(LoginContext);
    if (!context) {
        throw new Error("useLoginContext must be used within a LoginContext.Provider");
    }
    return context;
}