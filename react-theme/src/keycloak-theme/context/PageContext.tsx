import React, { createContext, useState, useContext, ReactNode } from "react";
import { Routes } from "../utilities/Routes";
import getStartPage from "keycloak-theme/utilities/getStartPage";

interface PageContextType {
  page: ReactNode;
  setPage: (element: ReactNode) => void;
  Routes: any;
  setBackPage: (pageCount?: number) => void;
  registerData: any; // change when register type is ready
  setRegisterData: (data: any) => void;
}

const PageContext = createContext<PageContextType | undefined>(undefined);

interface PageProviderProps {
  children: ReactNode;
}

const PageProvider: React.FC<PageProviderProps> = ({ children }) => {
  const defaultPage = getStartPage()({});
  const [page, setPage] = useState<ReactNode>(defaultPage);
  const [stack, setStack] = useState<ReactNode[]>([defaultPage]);
  const [registerData, setRegisterData] = useState<any>({});
  const setPageFunction = (element: ReactNode) => {
    setStack([...stack, element]);
    if (element !== stack[stack.length - 1]) setPage(element);
  };

  const setBackPage = (pageCount: number = 1) => {
    let newStack = [...stack];
    let count = pageCount;
    while (newStack.length && count--) {
      newStack.pop();
    }
    setStack(newStack);
    setPage(newStack[newStack.length - 1]);
  };

  const contextValue: PageContextType = {
    page,
    Routes,
    registerData,
    setRegisterData,
    setBackPage: setBackPage,
    setPage: setPageFunction,
  };

  return (
    <PageContext.Provider value={contextValue}>{children}</PageContext.Provider>
  );
};

export const usePageContext = () => {
  const context = useContext(PageContext);
  if (!context) {
    throw new Error("usePageContext must be used within a PageProvider");
  }
  return context;
};

export default PageProvider;
