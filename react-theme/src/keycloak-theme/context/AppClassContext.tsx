import React, { createContext, useState, useContext, ReactNode } from 'react';


interface AppClassContextType {
    appClass:any,
    setAppClass:any
}

const AppCLassContext = createContext<AppClassContextType | undefined>(undefined);


const AppThemeProvider: React.FC<any> = ({ children }) => {
    const [appClass, setAppClass] = useState<ReactNode>("app-md");

    const contextValue: AppClassContextType = {
        appClass,
        setAppClass
    };

    return (
        <AppCLassContext.Provider value={contextValue}>
            {children}
        </AppCLassContext.Provider>
    );
};

export const useAppClass = () => {
    const context = useContext(AppCLassContext);
    if (!context) {
        throw new Error('usePageContext must be used within a PageProvider');
    }
    return context;
};

export default AppThemeProvider;
