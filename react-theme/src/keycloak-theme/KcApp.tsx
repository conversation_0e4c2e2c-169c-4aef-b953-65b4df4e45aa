import "./KcApp.css";
import { lazy, Suspense, useEffect } from "react";
import type { KcContext } from "./kcContext";
import { useI18n } from "./i18n";
import Fallback, {
  defaultKcProps,
  type KcProps,
  type PageProps,
} from "keycloakify";
import Template from "./Template";
import "./pages/app.css";
import CustomApolloProvider from "./Providers/CustomApolloProvider";
import PageProvider from "./context/PageContext";
import MainHeaderWithoutRoute from "./components/headers/MainHeaderWithoutRoute";
import { config } from "ApplicationConfig";
import { useAppClass } from "./context/AppClassContext";
import { ToastProvider } from "./Providers/ToastProvider";
import DeleteAccountRootPage from "./pages/DeleteAccountContainer";
import OtpPage from "./pages/OtpPage";
// You can uncomment this to see the values passed by the main app before redirecting.
//import { foo, bar } from "./valuesTransferredOverUrl";
//console.log(`Values passed by the main app in the URL parameter:`, { foo, bar });

const Login = lazy(() => import("./pages/Login"));
// If you can, favor register-user-profile.ftl over register.ftl, see: https://docs.keycloakify.dev/realtime-input-validation
const Register = lazy(() => import("./pages/Register"));
const RegisterUserProfile = lazy(() => import("./pages/RegisterUserProfile"));
const Terms = lazy(() => import("./pages/Terms"));
const MyExtraPage1 = lazy(() => import("./pages/MyExtraPage1"));
const MyExtraPage2 = lazy(() => import("./pages/MyExtraPage2"));
const Info = lazy(() => import("./pages/Info"));
const Error = lazy(() => import("./pages/Error"));
const LogoutConfirm = lazy(() => import("./pages/LogoutConfirm"));
const LoginPageExpired = lazy(() => import("./pages/LoginPageExpired"));


// This is like editing the theme.properties
// https://github.com/keycloak/keycloak/blob/11.0.3/themes/src/main/resources/theme/keycloak/login/theme.properties
const kcProps: KcProps = {
  ...defaultKcProps,
  // NOTE: The classes are defined in ./KcApp.css
  // You can add your classes alongside thoses that are present in the default Keycloak theme...
  kcHtmlClass: [...defaultKcProps.kcHtmlClass, ""],
  kcFormGroupClass: [...defaultKcProps.kcFormGroupClass, "ui-form-group"],
  // ...or overwrite
  kcHeaderWrapperClass: "my-color my-font",
};

export default function App(props: { kcContext: KcContext }) {
  const { appClass } = useAppClass();
  const { kcContext } = props;

  const i18n = useI18n({ kcContext });
  const user = config.users.find((e) => e.type === "any");
  useEffect(() => {
    document.title = user?.app_name || "";
  }, []);

  if (i18n === null) {
    //NOTE: Locales not yet downloaded, we could as well display a loading progress but it's usually a matter of milliseconds.
    return null;
  }

  /*
   * Examples assuming i18n.currentLanguageTag === "en":
   * i18n.msg("access-denied") === <span>Access denied</span>
   * i18n.msg("foo") === <span>foo in English</span>
   */

  const pageProps: Omit<PageProps<any, typeof i18n>, "kcContext"> = {
    i18n,
    // Here we have overloaded the default template, however you could use the default one with:
    //Template: DefaultTemplate,
    Template,
    // Wether or not we should download the CSS and JS resources that comes with the default Keycloak theme.
    doFetchDefaultThemeResources: true,
    ...kcProps,
  };
  const { url } = kcContext;
  return (
    <Suspense>
      <CustomApolloProvider>
        <PageProvider>
          <div id="app" className={`app ${appClass}`}>
            <ToastProvider>
              <div id="card-container" className="card-container">
                {(() => {
                  switch (kcContext.pageId) {
                    case "login.ftl":
                      return <Login {...{ kcContext, ...pageProps }} />;
                    case "otp.ftl":
                      return (
                        <div id="front" className={`front card-side`}>
                          <MainHeaderWithoutRoute href="#" />
                          <div className="keycloack-message__container">
                            <OtpPage {...{ kcContext, ...pageProps }} />
                          </div>
                        </div>
                      );
                    case "register.ftl":
                      return <Register {...{ kcContext, ...pageProps }} />;
                    case "logout-confirm.ftl":
                      return (
                        <div id="front" className={`front card-side`}>
                          <MainHeaderWithoutRoute href="#" />
                          <div className="keycloack-message__container">
                            <LogoutConfirm {...{ kcContext, ...pageProps }} />
                          </div>
                        </div>
                      );
                    case "login-page-expired.ftl":
                      return (
                        <div id="front" className={`front card-side`}>
                          <MainHeaderWithoutRoute href="#" />
                          <div className="keycloack-message__container">
                            <LoginPageExpired
                              {...{ kcContext, ...pageProps }}
                            />
                          </div>
                        </div>
                      );
                    case "register-user-profile.ftl":
                      return (
                        <RegisterUserProfile {...{ kcContext, ...pageProps }} />
                      );
                    case "terms.ftl":
                      return <Terms {...{ kcContext, ...pageProps }} />;
                    case "my-extra-page-1.ftl":
                      return <MyExtraPage1 {...{ kcContext, ...pageProps }} />;
                    case "my-extra-page-2.ftl":
                      return <MyExtraPage2 {...{ kcContext, ...pageProps }} />;
                    case "info.ftl":
                      return (
                        <div id="front" className={`front card-side`}>
                          <MainHeaderWithoutRoute href="#" />
                          <div className="keycloack-message__container">
                            <Info {...{ kcContext, ...pageProps }} />
                          </div>
                        </div>
                      );
                    case "error.ftl": {
                      if (
                        new URLSearchParams(window?.location.search).get(
                          "go_to_page"
                        ) == "delete_account"
                      ) {
                        return <DeleteAccountRootPage  {...{ kcContext, ...pageProps }} />;
                      }
                      return (
                        <div id="front" className={`front card-side`}>
                          <MainHeaderWithoutRoute href="#" />
                          <div className="keycloack-message__container">
                            <Error {...{ kcContext, ...pageProps }} />
                          </div>
                        </div>
                      );
                    }
                    default:
                      return (
                        <div id="front" className={`front card-side`}>
                          <Fallback {...{ kcContext: kcContext as import("keycloakify").KcContextBase, ...pageProps }} />
                        </div>
                      );
                  }
                })()}
              </div>
            </ToastProvider>
          </div>
        </PageProvider>
      </CustomApolloProvider>
    </Suspense>
  );
}
