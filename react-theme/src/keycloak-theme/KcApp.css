:root {
  --color-primary: #1b2346;
  --color-primary-fg: white;
  --color-secondary: #62b356;
  --color-input-bg: #eef2f6;
  --color-input-fg: #5b7798;
}
.my-color {
  color: var(--color-primary);
}

.my-font {
  font-family: "Airbnb Cereal App";
}

.my-root-class.my-root-class {
  /* background: white; */
  overflow: hidden;
  background: white;
}

.my-root-class body {
  padding: 0 140px;
  background: url(./assets/img/bg-login.png) no-repeat bottom center fixed;
  background-size: contain;
}

.logo {
  width: 195px;
  height: 103px;
  background: url(./assets/img/sehati-logo.svg) no-repeat;
  background-size: contain;
}

.welcome-text-primary {
  color: var(--color-primary);
  font-size: 40px;
  margin-top: 24px;
  font-weight: bold;
}

.welcome-text-secondary {
  color: var(--color-secondary);
  font-size: 26px;
  font-weight: 500;
}

@media only screen and (max-width: 786px) {
  html {
    font-size: 10px;
  }
  .my-root-class body {
    padding: 0 20px;
  }

  #kc-form-login {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  .login-pf-page .login-pf-settings {
    min-width: 285px;
    margin: 0 auto;
  }
}

.login-pf-social-section {
  border-left: unset !important;
  border-right: unset !important;
}

.social-uae_pass:before {
  content: " ";
}

.card-pf {
  background: transparent;
}

.kc-page-title {
  font-weight: 500;
  font-size: 28px;
}
.instruction{
}