import React, { createContext, useContext, useState, ReactNode } from "react";
import { SuccessToast } from "../components/Toast/SuccessToast";
import { FailedToast } from '../components/Toast/FailedToast';
import { InfoToast } from "keycloak-theme/components/Toast/InfoToast";
import { WarningToast } from "keycloak-theme/components/Toast/WarningToast";

interface ToastContextType {
    pushToast: (body_message?: string, status?:"success"|"warning"|"info"|"failed", logo?: any) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

interface ToastProviderProps {
    children: ReactNode;
}

export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
    const [queue, setQueue] = useState<any[]>([]);
    const [count, setCount] = useState<number>(0);

    const removeToast = function (index: number) {
        setQueue((prevQueue) => {
            return prevQueue.filter((e) => e.key !== index);
        });
    };

    const pushToast = function (
        body_message: string = "",
        status: string = "success",
        logo: any
    ) {

        let new_queue = [...queue];
        const toastKey = count;
        new_queue.unshift({
            key: toastKey,
            component:
                status === "success" ? (
                    <SuccessToast
                        behavior={{
                            onCloseClicked: () => {
                                removeToast(count);
                            },
                        }}
                        elements={{ body_message: body_message }}
                        key={toastKey}
                    />
                ) : status === "failed" ? (
                    <FailedToast
                        behavior={{
                            onCloseClicked: () => {
                                removeToast(count);
                            },
                        }}
                        elements={{ body_message: body_message }}
                        key={toastKey}
                    />
                ):
                status === "info" ? (
                    <InfoToast
                        behavior={{
                            onCloseClicked: () => {
                                removeToast(count);
                            },
                        }}
                        elements={{ body_message: body_message }}
                        key={toastKey}
                    />
                ):(
                    <WarningToast
                        behavior={{
                            onCloseClicked: () => {
                                removeToast(count);
                            },
                        }}
                        elements={{ body_message: body_message }}
                        key={toastKey}
                    />
                )
        });
        setQueue(new_queue);
        setTimeout(() => {
            removeToast(count);
        }, 5000);
        setCount((count) => count + 1);
    };

    const contextValue: ToastContextType = {
        pushToast,
    };

    return (
        <ToastContext.Provider value={contextValue}>
            {children}
            
            <div className="toast-container">{queue.map((obj) => obj.component)}
            </div>
        </ToastContext.Provider>
    );
};

export const useToastContext = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToastContext must be used within a ToastProvider');
    }
    return context;
}