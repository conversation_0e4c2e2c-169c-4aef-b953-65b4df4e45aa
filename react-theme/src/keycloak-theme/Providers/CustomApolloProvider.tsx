import React, { PropsWithChildren } from 'react';
import { onError } from '@apollo/client/link/error';
import {
    ApolloClient,
    InMemoryCache,
    ApolloProvider,
    HttpLink,
    from,
} from '@apollo/client';
import { config } from 'ApplicationConfig';
import { kcContext, getKcProperties } from '../kcContext';

interface GraphQLError {
    message: string;
    location?: any;
    path?: any;
}

const ErrorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors) {
        graphQLErrors.forEach(({ message, location, path }: GraphQLError) => {
            // alert(`Graphql error ${message}`);
            //handel Graphql error 
        });
    }
});

interface CustomApolloProviderProps {
    children: React.ReactNode;
}
const CustomApolloProvider: React.FC<CustomApolloProviderProps> = ({
                                                                       children,
                                                                   }) => {
    const kcProperties = getKcProperties(kcContext);

    const apiUrl = kcProperties?.REACT_APP_API_URL || "https://api.sk-dev.sehacity.com/graphql";

    const link = from([
        ErrorLink,
        new HttpLink({ uri: apiUrl }),
    ]);

    const client = new ApolloClient({
        cache: new InMemoryCache(),
        link: link,
    });

    return <ApolloProvider client={client}>{children}</ApolloProvider>;
};

export default CustomApolloProvider;