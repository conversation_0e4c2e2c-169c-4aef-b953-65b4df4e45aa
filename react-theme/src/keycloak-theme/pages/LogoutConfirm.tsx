import IconPlant from "keycloak-theme/components/logo/IconPlant";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import type { PageProps } from "keycloakify";
import { useEffect } from "react";
import type { I18n } from "../i18n";
import type { KcContext } from "../kcContext";
export default function LogoutConfirm(
  props: PageProps<Extract<KcContext, { pageId: "logout-confirm.ftl" }>, I18n>
) {
  const { kcContext, i18n } = props;

  const { url, client, logoutConfirm } = kcContext;

  const { msgStr } = i18n;
  const { setAppClass } = useAppClass();
  useEffect(() => {
    setAppClass("app-sm");
  }, []);

  return (
    <div id="kc-logout-confirm" className="content-area">
      <div>
        <IconPlant width="139px" height="127.483px" />
      </div>
      <h1 className="custom-kc-page-title">Logging out</h1>
      <div className="custom-instruction"></div>
      <form action={url?.logoutConfirmAction} method="POST">
        <input type="hidden" name="session_code" value={logoutConfirm?.code} />
        <div className={"kcFormGroupClass"}>
          <div id="kc-form-options">
            <div className={"kcFormOptionsWrapperClass"}></div>
          </div>
          <div id="kc-form-buttons" className={"kcFormGroupClass"}>
            <input
              tabIndex={4}
              className={"Auth-card__button margin-top-5px"}
              name="confirmLogout"
              id="kc-logout"
              type="submit"
              value={msgStr("doLogout")}
            />
          </div>
        </div>
      </form>
      <div id="kc-info-message">
        {!logoutConfirm?.skipLink && client?.baseUrl && (
          <p>
            <a
              className="kc-backToApplication"
              href={client?.baseUrl}
              dangerouslySetInnerHTML={{ __html: msgStr("backToApplication") }}
            />
          </p>
        )}
      </div>
    </div>
  );
}
