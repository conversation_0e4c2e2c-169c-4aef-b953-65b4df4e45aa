// import { assert } from "../keycloakify/tools/assert";
import type { KcContext } from "../kcContext";
import type { I18n } from "../i18n";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { useEffect } from 'react';
import { PageProps } from "keycloakify";
import IconPlant from "keycloak-theme/components/logo/IconPlant";

export default function Info(props: PageProps<Extract<KcContext, { pageId: "info.ftl"; }>, I18n>) {
    const { kcContext, i18n  } = props;

    const { msg } = i18n;

    // assert(kcContext.message !== undefined);

    const {  message, skipLink, pageRedirectUri, actionUri, client } = kcContext;
    const {setAppClass } = useAppClass();
    useEffect(()=>{
        setAppClass("app-sm")        
    },[])
    return (
        <div id="kc-info-message">
                <div><IconPlant width="139px" height="127.483px"/></div>
                <p className="custom-kc-page-title">
                    {message?.summary}
                </p>
                {!skipLink && pageRedirectUri !== undefined ? (
                    <p>
                        <a className="kc-backToApplication" href={pageRedirectUri}>{msg("backToApplication")}</a>
                    </p>
                ) : actionUri !== undefined ? (
                    <p>
                        <a className="kc-backToApplication" href={actionUri}>{msg("proceedWithAction")}</a>
                    </p>
                ) : (
                    client?.baseUrl !== undefined && (
                        <p>
                            <a className="kc-backToApplication" href={client?.baseUrl}>{msg("backToApplication")}</a>
                        </p>
                    )
                )}
            </div>
    );
}