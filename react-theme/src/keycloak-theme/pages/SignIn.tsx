import React, { useState, useEffect } from "react";
import { InputText } from "../components/input/InputText";
import Icons8User from "../components/logo/Icons8User";
import Icons8Password from "../components/logo/Icons8Password";
import { InputPassword } from "../components/input/InputPassword";
import { usePageContext } from "../context/PageContext";
import Icons8Eye from "../components/logo/Icons8Eye";
import Icons8HiddenEye from "../components/logo/Icon8HiddenEye";
import { useLoginContext } from "keycloak-theme/context/LoginContext";
import { useConstCallback } from "keycloakify/lib/tools/useConstCallback";
import SocialAuthProviderOptions from "keycloak-theme/components/Auth/SocialAuthProviderOptions";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { isValidPhoneNumber } from "libphonenumber-js";
import IsoLogo from "../../App/IsoLogo.png";

interface SignInProps {}

const SignIn: React.FC<SignInProps> = () => {
  const { setPage, Routes } = usePageContext();
  const { setAppClass } = useAppClass();
  const { kcContext, user } = useLoginContext();
  const { social, url, login, message } = kcContext;

  const [formData, setFormData] = useState({
    email: "",
    password: "",
  });

  const [error, setError] = useState("");
  const [isButtonDisable, setIsButtonDisable] = useState(false);
  useEffect(() => {
    setAppClass(user.card_app_type || "app-md");

    setFormData({
      email: login?.username || "",
      password: login?.password || "",
    });

    setError(message?.summary || "");
  }, [user.card_app_type, login?.username, login?.password, message?.error]);

  const onSubmit = useConstCallback((e: React.FormEvent) => {
    e.preventDefault();
    setIsButtonDisable(true);
    function isValidEmail(email: string) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailPattern.test(email);
    }
    let error = false;

    if (!formData.email.trim()) {
      error = true;
    } else if (
      formData.email.trim()[0] === "+" &&
      !isValidPhoneNumber("" + formData.email.trim())
    ) {
      error = true;
    } else if (
      formData.email.trim()[0] !== "+" &&
      !isValidEmail(formData.email.trim())
    ) {
      error = true;
    }
    if (!formData.password) {
      error = true;
    }
    setError(error ? "The email or password you entered is incorrect" : "");
    if (error) {
      setIsButtonDisable(false);
      return;
    }

    const formElement = e.target as HTMLFormElement;

    formElement
      .querySelector("input[name='email']")
      ?.setAttribute("name", "username");

    formElement.submit();
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: newValue,
    }));
  };

  return (
    <div>
      <section className="welcome">
        <p className="welcome__text">Welcome to ioHealth</p>
        <h1 className="first-heading">Your Health Superapp</h1>
      </section>

      <section className="Auth-card">
        <div className="Auth-card__title">Sign in</div>

        <form
          id="kc-form-login"
          onSubmit={onSubmit}
          action={url.loginAction}
          method="post"
        >
          <InputText
            inputProps={{
              id: "email",
              name: "email",
              placeholder: "Mobile / Email",
              value: formData.email,
              onChange: handleChange,
            }}
            classes={{
              input_rightLogo_classes: error ? `input__right-logo--error` : "",
              input_classes: `${error ? "input--error" : ""}`,
            }}
            elements={{
              rightLogo: <Icons8User width="15px" height="15.683px" />,
            }}
          />

          <InputPassword
            inputProps={{
              id: "pass",
              name: "password",
              placeholder: "Password",
              value: formData.password,
              onChange: handleChange,
              autoComplete: "off",
            }}
            classes={{
              input_rightLogo_classes: error ? `input__right-logo--error` : "",
              input_leftLogo_classes: error
                ? `input__right-logo--error`
                : "custom-logo",
              input_classes: `${error ? "input--error" : ""}`,
            }}
            elements={{
              hideLogo: <Icons8HiddenEye width={"20.648px"} height="13.766" />,
              showLogo: <Icons8Eye width="20.648" height="13.766" />,
              rightLogo: <Icons8Password width="15px" height="15.683px" />,
            }}
          />

          {/* {user.display_forget_password ? ( */}
          <a
            href="#"
            className="Auth-card__link"
            role="button"
            onClick={() => setPage(Routes.reset_pass)}
          >
            Forgot password?
          </a>
          {/* ) : null} */}

          <button
            className={`Auth-card__button ${
              isButtonDisable ? "Auth-card__button--disable" : ""
            }`}
          >
            Sign in
          </button>

          {error ? (
            <div className="error-message error-login">{error}</div>
          ) : (
            ""
          )}
        </form>
      </section>

      {user.display_social_provider ? (
        <SocialAuthProviderOptions
          hrefs={social?.providers?.map(
            (e: { loginUrl: string }) => e.loginUrl
          )}
        />
      ) : null}

      {user.display_signup ? (
        <div className="auth-container">
          Don’t have an account?
          <a
            href="#"
            tabIndex={0}
            className="auth__link"
            role="button"
            onClick={() => setPage(Routes.sign_up)}
          >
            {" Sign up"}
          </a>
        </div>
      ) : null}
      <img
        src={IsoLogo}
        alt="Button Image"
        style={{ width: "250px", height: "60px", marginTop: "10px" }}
      />
    </div>
  );
};

export default SignIn;
