import React, { useEffect, useState } from "react";
import { usePageContext } from "../context/PageContext";
import {
  REQUEST_PASSWORD_RESET,
  CONFIRM_PASSWORD_REST_OTP,
} from "../GraphQL/Mutations";
import { useMutation } from "@apollo/client";
import OtpForm from "../components/OtpForm";
import { config } from "ApplicationConfig";
import { useAppClass } from "keycloak-theme/context/AppClassContext";

interface VerificationProps {
  appType?: string;
  passwordResetMethod?: string;
  username?: string;
  sessionToken?: string;
  vendor?: string;
}

export default function Verification(props: VerificationProps) {
  const { setAppClass } = useAppClass();
  const { setPage, Routes } = usePageContext();
  const [error, setError] = useState("");
  const [fetchRequestPasswordReset] = useMutation(REQUEST_PASSWORD_RESET);
  const [fetchConfirmPasswordResetOTP] = useMutation(CONFIRM_PASSWORD_REST_OTP);
  const [sessionToken, setSessionToken] = useState(props.sessionToken);

  const handelResend = async () => {
    let response = await fetchRequestPasswordReset({
      variables: {
        appType: props.appType,
        passwordResetMethod: props.passwordResetMethod,
        username: props.username,
        vendor: props.vendor,
      },
    });
    const data = response?.data?.requestPasswordReset;
    if (data.errors?.length) {
      setError(data.errors.message);
      return;
    }
    setSessionToken(data.sessionToken);
  };

  const handelVerify = async (otp: string) => {
    let response = await fetchConfirmPasswordResetOTP({
      variables: {
        appType: props.appType,
        passwordResetMethod: props.passwordResetMethod,
        username: props.username,
        verificationCode: otp,
        sessionToken: sessionToken,
        vendor: props.vendor,
      },
    });
    let status = response.data.confirmPasswordRestOtp.success;
    if (!status) {
      setError("Invalid OTP");
      return;
    }
    setPage(
      Routes.create_new_pass({
        bodyProps: {
          token: sessionToken,
          username: props.username,
          appType: props.appType,
          passwordResetMethod: props.passwordResetMethod,
          vendor: props.vendor,
        },
      })
    );
  };

  useEffect(() => {
    setAppClass("app-md");
  }, []);

  return (
    <OtpForm
      seconds={config.RESEND_OTP_FOR_FORGETPASSWORD_IN_SECOUND}
      onSubmit={handelVerify}
      onResend={handelResend}
      message={`Please enter the 4-digit verification code that was sent to`}
      username={props.username ?? "your account"}
      outterError={error}
    />
  );
}
