import { useMutation } from "@apollo/client";
import { config } from "ApplicationConfig";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { useEffect, useState } from "react";
import {
  CONFIRM_VERIFICATION,
  DELETE_CUSTOMER_BY_EMAIL,
  DELETE_CUSTOMER_BY_MOBILE,
  REQUEST_OTP_FOR_DELETE_ACCOUNT,
} from "../GraphQL/Mutations";
import { useToastContext } from "../Providers/ToastProvider";
import OtpForm from "../components/OtpForm";
import { usePageContext } from "../context/PageContext";
import { IdentityTypes } from "keycloak-theme/constants";

interface DeleteAccountVerificationProps {
  identity: string;
  reason: string;
  identityType: string;
  sessionToken: string;
}
export const defaultPropsForRoute = {
  identity: "",
  reason: "",
  identityType: "email",
  sessionToken: "",
};

export default function DeleteAccountVerification(
  props: DeleteAccountVerificationProps
) {
  const { setAppClass } = useAppClass();
  const { pushToast } = useToastContext();
  const { setPage, Routes } = usePageContext();
  const [error, setError] = useState("");
  const [sessionToken, setSessionToken] = useState(props.sessionToken);
  const [fetchResendOTP] = useMutation(REQUEST_OTP_FOR_DELETE_ACCOUNT);
  const [fetchConfirmVerification] = useMutation(CONFIRM_VERIFICATION);
  const [fetchDeleteCustomer, { loading: isLoading }] = useMutation(
    props.identityType === IdentityTypes.email
      ? DELETE_CUSTOMER_BY_EMAIL
      : DELETE_CUSTOMER_BY_MOBILE
  );
  let userMethod = props.identityType === "mobile" ? "phoneNumber" : "email";

  const handelResend = async function () {
    try {
      let resp = await fetchResendOTP({
        variables: {
          [userMethod]: props.identity.trim(),
        },
      });
      let data = resp.data.requestOtpForDeleteAccount;
      setSessionToken(data.sessionToken);
    } catch (e) {
      pushToast("Something went wrong. Please try again.", "failed");
    }
  };

  const handelVerify = async function (otp: string) {
    try {
      let response = await fetchConfirmVerification({
        variables: {
          [userMethod]: props.identity.trim(),
          sessionToken: sessionToken,
          verificationCode: otp,
        },
      });
      let status = response.data.confirmVerification.success;
      if (!status) {
        setError(
          response.data.confirmVerification.accountErrors.message ||
            "verification_code is not valid."
        );
        return;
      }
      let resp = await fetchDeleteCustomer({
        variables: {
          sessionToken,
          reason: props.reason,
          identity: props.identity,
        },
      });
      if (resp?.data?.customerCreate?.patientErrors?.length) {
        pushToast("Something went wrong. Please try again.", "failed");
        return;
      } else {
        setPage(Routes.delete_account_Completed({}));
      }
    } catch (e) {
      pushToast("Something went wrong. Please try again.", "failed");
    }
  };

  useEffect(() => {
    setAppClass("app-md");
  }, []);

  return (
    <OtpForm
      seconds={config.RESEND_OTP_FOR_FORGETPASSWORD_IN_SECOUND}
      onSubmit={handelVerify}
      onResend={handelResend}
      message={`Please enter the 4-digit verification code that was sent to`}
      username={props.identity.trim() ?? "your account"}
      outterError={error}
    />
  );
}
