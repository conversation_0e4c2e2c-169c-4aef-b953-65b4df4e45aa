import React, { useState, useEffect, ChangeEvent, FormEvent } from "react";
import { InputText } from "../components/input/InputText";
import InputAutocomplete, {
  Option,
} from "../components/input/InputAutocomplete";
import Icons8User from "../components/logo/Icons8User";
import { usePageContext } from "../context/PageContext";
import { REQUEST_PASSWORD_RESET } from "../GraphQL/Mutations";
import { useLazyQuery, useMutation } from "@apollo/client";
import { useLoginContext } from "keycloak-theme/context/LoginContext";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { isValidPhoneNumber } from "libphonenumber-js";
import { useToastContext } from "keycloak-theme/Providers/ToastProvider";
import { FETCH_VENDORS } from "../GraphQL/Queries";
import { extractAccountErrorMessage } from "keycloak-theme/utilities/errorResponseHandlers";
import {
  GENERAL_ERROR_MSG,
  RESET_PASS_EMAIL_ERROR_MSG,
  RESET_PASS_VENDOR_ERROR_MSG,
} from "keycloak-theme/constants";
import LoadingSpinner from "keycloak-theme/components/LoadingSpinner";
import { isValidEmail } from "keycloak-theme/utilities/validators";

interface ResetPassProps {}

export default function ResetPass(props: ResetPassProps) {
  const { setPage, Routes } = usePageContext();
  const { setAppClass } = useAppClass();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { pushToast } = useToastContext();
  useEffect(() => {
    setAppClass("app-md");
  }, []);

  const [formData, setFormData] = useState({
    email: "",
    vendor: "",
  });

  const userNameMethod = {
    MOBILE: "MOBILE",
    EMAIL: "EMAIL",
  };

  const [formErrorMessage, setFormErrorMessage] = useState("");
  const { user } = useLoginContext();
  const [fetchRequestPasswordReset] = useMutation(REQUEST_PASSWORD_RESET);
  const [fetchVendors, { data, loading: isLoadingVendors }] = useLazyQuery(
    FETCH_VENDORS,
    {
      nextFetchPolicy: "no-cache",
    }
  );

  const isVendorApp = user.graphql_type === "VENDOR";

  const vendorOptions =
    data?.vendors?.edges?.map((edge: any) => ({
      label: edge.node.name,
      value: edge.node.id,
    })) ?? [];

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: newValue,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    try {
      setIsSubmitting(true);

      let validationError = "";
      if (!formData.email.trim()) {
        validationError = RESET_PASS_EMAIL_ERROR_MSG;
      } else if (
        formData.email.trim()[0] === "+" &&
        !isValidPhoneNumber("" + formData.email.trim())
      ) {
        validationError = RESET_PASS_EMAIL_ERROR_MSG;
      } else if (
        formData.email.trim()[0] !== "+" &&
        !isValidEmail(formData.email.trim())
      ) {
        validationError = RESET_PASS_EMAIL_ERROR_MSG;
      }

      if (isVendorApp && !formData.vendor) {
        validationError = RESET_PASS_VENDOR_ERROR_MSG;
      }

      setFormErrorMessage(validationError);

      const userMethod =
        formData.email.trim()[0] === "+"
          ? userNameMethod.MOBILE
          : userNameMethod.EMAIL;

      if (validationError) {
        setIsSubmitting(false);
        return;
      }

      let response = await fetchRequestPasswordReset({
        variables: {
          appType: user.graphql_type,
          passwordResetMethod: userMethod,
          username: formData.email.trim(),
          vendor: isVendorApp ? formData.vendor.trim() : null,
        },
      });

      const data = response?.data?.requestPasswordReset;

      if (data.accountErrors.length) {
        setIsSubmitting(false);
        setFormErrorMessage(extractAccountErrorMessage(response));
        return;
      }

      setPage(
        Routes.verification({
          bodyProps: {
            sessionToken: data.sessionToken,
            username: formData.email.trim(),
            appType: user.graphql_type,
            passwordResetMethod: userMethod,
            vendor: isVendorApp ? formData.vendor.trim() : null,
          },
        })
      );
    } catch (e) {
      pushToast(GENERAL_ERROR_MSG, "failed");
      setIsSubmitting(false);
    }
  };

  const handleVendorSelected = (vendor: Option | null) => {
    setFormData((value) => ({ ...value, vendor: vendor?.value ?? "" }));
  };

  const handleSearch = (searchText: string) => {
    fetchVendors({
      variables: {
        first: 10,
        filter: {
          isActive: true,
          name_Icontains: searchText,
        },
      },
    });
  };

  return (
    <div className="Auth-operation-card">
      <div className="Auth-operation__title">Reset Password</div>
      <div className="Auth-operation__description">
        Please enter your registered email/mobile number
      </div>
      <form onSubmit={handleSubmit}>
        <InputText
          inputProps={{
            id: "email",
            name: "email",
            placeholder: "Mobile / Email",
            value: formData.email,
            onChange: handleChange,
          }}
          classes={{
            input_rightLogo_classes: formErrorMessage
              ? `input__right-logo--error`
              : "",
            input_classes: `${formErrorMessage ? "input--error" : ""}`,
          }}
          elements={{
            rightLogo: <Icons8User width="15px" height="15.683px" />,
          }}
        />
        {
          isVendorApp && <div className="Input-component-Wrapper">
          <InputAutocomplete
            placeholder="Search vendors (min 3 chars)..."
            noOptionsText="No vendors found"
            options={vendorOptions}
            hasError={!!formErrorMessage}
            isLoading={isLoadingVendors}
            onSelect={handleVendorSelected}
            onSearch={handleSearch}
          />
        </div>
        }
        <div>
          <button
            type="submit"
            className={`Auth-card__button ${
              isSubmitting ? "Auth-card__button--disable" : ""
            }`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <LoadingSpinner
                style={{ padding: 0 }}
                iconProps={{ width: "20px", height: "20px", fill: "white" }}
              />
            ) : (
              "Continue"
            )}
          </button>
        </div>
        {formErrorMessage ? (
          <div className="error-message error-login">{formErrorMessage}</div>
        ) : null}
      </form>
    </div>
  );
}
