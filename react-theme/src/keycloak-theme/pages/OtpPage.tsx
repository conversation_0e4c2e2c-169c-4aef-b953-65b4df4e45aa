import { useAppClass } from "keycloak-theme/context/AppClassContext";
import type { PageProps } from "keycloakify";
import { useEffect, useState } from "react";
import type { I18n } from "../i18n";
import type { KcContext } from "../kcContext";
import { InputOTP } from "keycloak-theme/components/input/InputOTP";

export default function OtpPage(
    props: PageProps<Extract<KcContext, { pageId: "otp.ftl" }>, I18n>
) {
    const { kcContext } = props;
    const { url, message, AUTH_METHOD, CONTACT_INFO } = kcContext;

    const { setAppClass } = useAppClass();

    const [otpValue, setOtpValue] = useState("");
    const [localError, setLocalError] = useState("");
    const [isSubmitting, setIsSubmitting] = useState(false);

    useEffect(() => {
        setAppClass("app-sm");
    }, [setAppClass]);


    const displayError = localError || (message?.type === "error" && message.summary)
    const hasError = !!localError || (message?.type === "error" && message.summary);

    const otpLength = AUTH_METHOD === "AUTHENTICATOR" ? 6 : 4;

    const validateOtp = (value: string): string => {
        if (!value) {
            return "OTP code is required";
        }

        if (!/^\d+$/.test(value)) {
            return "OTP code must contain only numbers";
        }

        if (value.length !== otpLength) {
            return `OTP code must be exactly ${otpLength} digits`;
        }

        return "";
    };

    const handleOtpChange = (value: string) => {
        setOtpValue(value);

        if (localError) {
            setLocalError("");
        }
    };

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();

        const validationError = validateOtp(otpValue);
        if (validationError) {
            setLocalError(validationError);
            return;
        }

        setLocalError("");
        setIsSubmitting(true);

        const formElement = e.target as HTMLFormElement;

        formElement.submit();

    };

    const getInstructionText = () => {
        switch (AUTH_METHOD) {
            case "MOBILE":
                return `Enter the verification code sent to your mobile phone (${CONTACT_INFO})`;
            case "EMAIL":
                return `Enter the verification code sent to your email (${CONTACT_INFO})`;
            case "AUTHENTICATOR":
                return "Enter the code from your authenticator app";
            default:
                return "Enter your verification code";
        }
    };

    const getPageTitle = () => {
        switch (AUTH_METHOD) {
            case "MOBILE":
                return "Mobile Verification"
            case "EMAIL":
                return "Email Verification";
            case "AUTHENTICATOR":
                return "Authenticator Verification";
            default:
                return "Two-Factor Verification";
        }
    };

    return (
        <div className="keycloak-message__container">
            <div className="Auth-card">
                <h1 className="Auth-card__title">{getPageTitle()}</h1>

                <p className="custom-instruction">
                    {getInstructionText()}
                </p>


                {message?.type !== "error" && (
                    <div className="error-message error-login">
                        {message?.summary}
                    </div>
                )}

                <form
                    id="otp-form"
                    action={url.loginAction}
                    method="post"
                    onSubmit={handleSubmit}>
                    <div className="Input-component-Wrapper">
                        <label className="input__label">
                            Verification Code ({otpLength} digits)
                        </label>

                        <InputOTP
                            numInputs={otpLength}
                            onChange={handleOtpChange}
                        />

                        <input
                            type="hidden"
                            name="otp"
                            value={otpValue}
                        />

                        {hasError && (
                            <span className="error-message" role="alert">
                                {displayError}
                            </span>
                        )}
                    </div>

                    <input
                        className={`Auth-card__button ${isSubmitting || otpValue.length !== otpLength ? "Auth-card__button--disable" : ""}`}
                        name="login"
                        id="kc-login"
                        type="submit"
                        value={isSubmitting ? "Verifying..." : "Verify Code"}
                        disabled={isSubmitting || otpValue.length !== otpLength}
                    />
                </form>

                {url?.loginResetCredentialsUrl && (
                    <a
                        href={url?.loginRestartFlowUrl}
                        className="Auth-card__link back-to-login__link"
                    >
                        ← Back to Login
                    </a>
                )}
            </div>
        </div>
    );
}