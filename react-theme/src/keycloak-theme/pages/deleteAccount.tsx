import { useMutation } from "@apollo/client";
import {
  REQUEST_OTP_FOR_DELETE_ACCOUNT,
  VERIFY_CREDENTIALS,
} from "keycloak-theme/GraphQL/Mutations";
import { useToastContext } from "keycloak-theme/Providers/ToastProvider";
import LoadingSpinner from "keycloak-theme/components/LoadingSpinner";
import {
  GENERAL_ERROR_MSG,
  IdentityTypes,
  RESET_PASS_EMAIL_ERROR_MSG,
} from "keycloak-theme/constants";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { extractAccountErrorMessage } from "keycloak-theme/utilities/errorResponseHandlers";
import { isValidEmail } from "keycloak-theme/utilities/validators";
import { isValidPhoneNumber } from "libphonenumber-js";
import React, { ChangeEvent, FormEvent, useEffect, useState } from "react";
import { InputText } from "../components/input/InputText";
import Icons8User from "../components/logo/Icons8User";
import { usePageContext } from "../context/PageContext";
import InputCheckbox from "keycloak-theme/components/input/InputCheckbox";
import Warning from "keycloak-theme/components/logo/Warning";
import { InputTextArea } from '../components/input/InputTextArea';

interface DeleteAccountProps {}
type FormErrors = {
  identity?: string;
  reason?: string;
};

const DeleteAccount: React.FC<DeleteAccountProps> = () => {
  // Context
  const { setPage, Routes } = usePageContext();
  const { pushToast } = useToastContext();
  const { setAppClass } = useAppClass();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formErrors, setFormErrors] = useState<FormErrors>({
    identity: "",
    reason: "",
  });

  useEffect(() => {
    setAppClass("app-md");
  }, []);

  const [formData, setFormData] = useState({
    identity: "",
    vendor: "",
    reason: "",
    isDeleteConfirmed: false,
  });

  const [fetchRequestOtpForDeleteAccount] = useMutation(
    REQUEST_OTP_FOR_DELETE_ACCOUNT
  );
  const isDeleteButtonDisabled = isSubmitting || !formData.isDeleteConfirmed;

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: newValue,
    }));
  };
  const handleTextAreaChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: value,
    }));
  }

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();

    try {
      const _errors = {
        identity: "",
        reason: "",
      };
      setIsSubmitting(true);
      const identity = formData.identity.trim();
      const isMobileIdentity = identity[0] === "+";
      const isEmailIdentity = !isMobileIdentity;
      const identityType = isMobileIdentity
        ? IdentityTypes.mobile
        : IdentityTypes.email;

      if (!identity) {
        _errors["identity"] = RESET_PASS_EMAIL_ERROR_MSG;
      } else if (isMobileIdentity && !isValidPhoneNumber("" + identity)) {
        _errors["identity"] = RESET_PASS_EMAIL_ERROR_MSG;
      } else if (isEmailIdentity && !isValidEmail(identity)) {
        _errors["identity"]= RESET_PASS_EMAIL_ERROR_MSG;
      }
      if (!formData.reason) {
        _errors["reason"] = "Please enter a reason";
      }
      else if (formData.reason.length >2000) {
        _errors["reason"] = "Reason should not exceed 2000 characters";
      }

      setFormErrors(_errors);

      if (_errors?.identity || _errors?.reason) {
        setIsSubmitting(false);
        return;
      }
      let userMethod = isMobileIdentity ? "phoneNumber" : "email";

      let response = await fetchRequestOtpForDeleteAccount({
        variables: {
          [userMethod]: identity,
        },
      });

      const data = response?.data?.requestOtpForDeleteAccount;

      setIsSubmitting(false);
      const errors = data?.accountErrors;
      if (errors.length > 0) {
        pushToast(errors[0], "failed");
        return;
      }
      console.log({
        identity,
        identityType,
        reason: formData.identity,
        sessionToken: data.sessionToken,
      });
      setPage(
        Routes.delete_account_verification({
          bodyProps: {
            identity,
            identityType,
            reason: formData.reason,
            sessionToken: data.sessionToken,
          },
        })
      );
    } catch (e) {
      pushToast(GENERAL_ERROR_MSG, "failed");
    }
  };

  return (
    <div>
      <div><Warning width="130px"/></div>
      <div className="Auth-operation-card">
      
        <div className="Auth-operation__title warning">Delete Your Account</div>
        <div className="Auth-operation__description">
          Please enter your registered email/mobile number
        </div>
        <form onSubmit={handleSubmit}>
          <InputText
            inputProps={{
              id: "identity",
              name: "identity",
              placeholder: "Mobile / Email",
              value: formData.identity,
              onChange: handleChange,
            }}
            classes={{
              input_rightLogo_classes: formErrors?.identity
                ? `input__right-logo--error`
                : "",
              input_classes: `${formErrors?.identity ? "input--error" : ""}`,
            }}
            elements={{
              rightLogo: <Icons8User width="15px" height="15.683px" />,
              inputMessage: formErrors?.identity && (
                <div className="error-message">{formErrors?.identity}</div>
              )
            }}
          />
          
          <InputTextArea
            inputProps={{
              id: "reason",
              name: "reason",
              placeholder: "Reason",
              value: formData.reason,
              rows: 4,
              onChange: handleTextAreaChange,
            }}
            classes={{
              input_classes: `${formErrors?.reason ? "input--error" : ""}`,
            }}
            elements={{
              inputMessage: formErrors?.reason && (
                <div className="error-message">{formErrors?.reason}</div>
              )
            }}
          />
          <p className="terms-condtions">
            <InputCheckbox
              inputProps={{
                name: "isDeleteConfirmed",
                checked: formData.isDeleteConfirmed,
                onChange: handleChange,
                id: "isDeleteConfirmed",
              }}
              classes={{
                input_classes: "checkbox",
                input_Wrapper_classes: "checkbox-wrapper book",
                checkbox: "checkbox-label mg-left-5",
              }}
              elements={{
                label: "I confirm that I need to delete my account",
                
              }}
            />
          </p>
          <div>
            <button
              type="submit"
              className={`Auth-card__button ${
                isDeleteButtonDisabled ? "Auth-card__button--disable" : ""
              }`}
              disabled={isDeleteButtonDisabled}
            >
              {isSubmitting ? (
                <LoadingSpinner
                  style={{ padding: 0 }}
                  iconProps={{
                    width: "20px",
                    height: "20px",
                    fill: "white",
                  }}
                />
              ) : (
                "Continue"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default DeleteAccount;
