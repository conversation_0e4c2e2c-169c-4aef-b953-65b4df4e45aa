import type { Kc<PERSON>ontext } from "../kcContext";
import type { I18n } from "../i18n";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { useEffect } from 'react';
import type { PageProps } from "keycloakify";
import IconPlant from "keycloak-theme/components/logo/IconPlant";
export default function Error(props:PageProps<Extract<KcContext, { pageId: "error.ftl" }>, I18n>) {
    const { kcContext, i18n } = props;

    const { message, client } = kcContext;

    const { msg } = i18n;
    const {setAppClass } = useAppClass();
    useEffect(()=>{
        setAppClass("app-sm")        
    },[])
    return (
        
        <div id="kc-error-message">
            <div><IconPlant width="139px" height="127.483px"/></div>
            <h1 className="custom-kc-page-title">We are sorry...</h1>
            <p className="custom-instruction">{message?.summary}</p>
            {client !== undefined && client?.baseUrl !== undefined && (
                <p>
                    <a className="kc-backToApplication" id="backToApplication" href={client.baseUrl}>
                        {msg("backToApplication")}
                    </a>
                </p>
            )}
            </div>
    );
}