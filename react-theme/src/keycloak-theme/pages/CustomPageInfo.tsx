// import { assert } from "../keycloakify/tools/assert";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import  { FC, ReactNode, useEffect } from 'react';
type ICustomInfoCardProps = {
    title?: string;
    icon?: ReactNode;
    description?: string;
}
export const CustomInfo: FC<ICustomInfoCardProps> = props => {
    const { title, description, icon } = props;

    // assert(kcContext.message !== undefined);

    const {setAppClass } = useAppClass();
    useEffect(()=>{
        setAppClass("app-sm")        
    },[])
    return (
        <div id="kc-info-message">
                <div>{icon}</div>
                <p className="custom-kc-page-title primary">
                    {title}
                </p>
                <p className="custom-instruction info__title">{description}</p>
        </div>
    );
}