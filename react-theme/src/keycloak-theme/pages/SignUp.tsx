import React, { useEffect, useState, ChangeEvent, FormEvent } from "react";
import { InputText } from "../components/input/InputText";
import Icons8User from "../components/logo/Icons8User";
import Icons8Password from "../components/logo/Icons8Password";
import { InputPassword } from "../components/input/InputPassword";
import InputRadio from "../components/input/InuptRadio";
import InputCheckbox from "../components/input/InputCheckbox";
import Icons8Eye from "../components/logo/Icons8Eye";
import { usePageContext } from "../context/PageContext";
import {
  CHECK_CUSTOMER_ACCOUNT_EXIST,
  VERIFY_CREDENTIALS,
} from "../GraphQL/Mutations";
import { useMutation } from "@apollo/client";
import Icons8HiddenEye from "keycloak-theme/components/logo/Icon8HiddenEye";
import { useLoginContext } from "keycloak-theme/context/LoginContext";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import SocialAuthProviderOptions from "keycloak-theme/components/Auth/SocialAuthProviderOptions";
import { isValidPhoneNumber } from "libphonenumber-js";
import { useToastContext } from "../Providers/ToastProvider";
import IsoLogo from "../../App/IsoLogo.png";

const today_date_string_format = function () {
  const currentDate = new Date();
  const day = currentDate.getDate();
  const month = currentDate.getMonth() + 1;
  const year = currentDate.getFullYear();
  return `${year}-${month.toString().padStart(2, "0")}-${day
    .toString()
    .padStart(2, "0")}`;
};

const userNameMethod = {
  MOBILE: "MOBILE",
  EMAIL: "EMAIL",
};

interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string[];
  confirmPassword?: string;
  dateOfBirth?: string;
  gender?: string;
  termsAccepted?: string;
}

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  dateOfBirth: string;
  gender: string;
  termsAccepted: boolean;
}

const SignUp: React.FC = () => {
  const { setAppClass } = useAppClass();
  const { kcContext } = useLoginContext();
  const { pushToast } = useToastContext();
  const { social } = kcContext;
  const { setPage, Routes, registerData, setRegisterData } = usePageContext();
  interface FormData {
    firstName: string;
    lastName: string;
    email: string;
    password: string;
    confirmPassword: string;
    dateOfBirth: string;
    gender: string;
    termsAccepted: boolean;
  }

  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    dateOfBirth: "",
    gender: "",
    termsAccepted: false,
  });
  const [isButtonDisable, setIsButtonDisable] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({
    firstName: "",
    lastName: "",
    email: "",
    password: [],
    confirmPassword: "",
    dateOfBirth: "",
    gender: "",
  });

  const [fetchCheckCustomerAccountExist, { loading: loading1 }] = useMutation(
    CHECK_CUSTOMER_ACCOUNT_EXIST
  );
  const [fetchVerifyCredentials, { loading: loading2 }] =
    useMutation(VERIFY_CREDENTIALS);

  useEffect(() => {
    setAppClass("app-lg");
    if (Object.keys(registerData).length) setFormData(registerData);
  }, []);

  useEffect(() => {
    setRegisterData({
      ...formData,
      password: "",
      confirmPassword: "",
    });
  }, [formData]);
  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    const newValue = type === "checkbox" ? checked : value;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: newValue,
    }));
  };

  const handelGenderFocus = function (gend: string) {
    setFormData((prevFormData) => ({
      ...prevFormData,
      gender: gend,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    function isValidEmail(email: string) {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailPattern.test(email);
    }

    function isUserAdult(birthDate: string) {
      const today = new Date();
      const userBirthDate = new Date(birthDate);
      const userAgeInMilliseconds = today.getTime() - userBirthDate.getTime();
      const userAgeInYears =
        userAgeInMilliseconds / (1000 * 60 * 60 * 24 * 365);

      return userAgeInYears >= 18;
    }
    e.preventDefault();
    setIsButtonDisable(true);
    // Validate firstName
    const newErrors: FormErrors = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = "First name is required";
    } else if (formData.firstName.trim().length > 30) {
      newErrors.firstName = "Less than 30 chars";
    } else {
      newErrors.firstName = "";
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = "Last name is required";
    } else if (formData.lastName.trim().length > 30) {
      newErrors.lastName = "Less than 30 chars";
    } else {
      newErrors.lastName = "";
    }
    if (!formData.email.trim()) {
      newErrors.email = "Email / Mobile is required";
    } else if (
      formData.email.trim()[0] === "+" &&
      !isValidPhoneNumber("" + formData.email.trim())
    ) {
      newErrors.email = "Invalid Phone Number";
    } else if (
      formData.email.trim()[0] !== "+" &&
      !isValidEmail(formData.email.trim())
    ) {
      newErrors.email = "Invalid email";
    } else {
      newErrors.email = "";
    }
    newErrors.password = [];

    if (!formData.password) {
      newErrors.password?.push("Password is required");
    }

    if (formData.password.length < 8) {
      newErrors.password?.push("Password must be at least 8 characters long");
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(formData.password)) {
      newErrors.password?.push(
        "Password must contain at least one uppercase letter"
      );
    }

    // Check for at least one special character
    if (!/[!@#$%^&*]/.test(formData.password)) {
      newErrors.password?.push(
        "Password must contain at least one special character (!@#$%^&*)"
      );
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Confirm Password is required";
    } else if (formData.confirmPassword.length < 8) {
      newErrors.confirmPassword = "Must be at least 8 characters ";
    } else if (formData.confirmPassword !== formData.password) {
      newErrors.confirmPassword =
        "Password and Confirm password should be same";
    } else {
      newErrors.confirmPassword = "";
    }
    if (!formData.dateOfBirth) {
      newErrors.dateOfBirth = "Date of Brith is required";
    } else if (new Date(formData.dateOfBirth) > new Date()) {
      newErrors.dateOfBirth = "Invalid Date";
    } else if (!isUserAdult(formData.dateOfBirth)) {
      newErrors.dateOfBirth = "User Should be older than 18";
    } else {
      newErrors.dateOfBirth = "";
    }
    if (!formData.gender) {
      newErrors.gender = "Please select a value for gender";
    } else {
      newErrors.gender = "";
    }
    // console.log(formData.termsAccepted)
    if (!formData.termsAccepted) {
      newErrors.termsAccepted = "Terms and Conditions Not Accepted";
    } else {
      newErrors.termsAccepted = "";
    }
    setErrors({ ...errors, ...newErrors });
    if (Object.values(newErrors).some((error) => error.length > 0)) {
      setIsButtonDisable(false);
      return;
    }
    let userMethod =
      formData.email.trim()[0] === "+" ? "contactNumber" : "email";
    try {
      let response = await fetchCheckCustomerAccountExist({
        variables: {
          [userMethod]: formData.email.trim(),
        },
      });
      let data = response.data.checkCustomerAccountExist;
      const ResponseUserMethod =
        formData.email.trim()[0] === "+"
          ? "isContactNumberExists"
          : "isEmailExists";
      if (data[ResponseUserMethod] === null) {
        setErrors({ email: data.accountErrors[0]["message"] });
        setIsButtonDisable(false);
        return;
      } else if (data[ResponseUserMethod] === true) {
        setErrors({
          email:
            userMethod === "email"
              ? "Email Already in Use"
              : "Phone Already in Use",
        });
        setIsButtonDisable(false);
        return;
      }
      userMethod = formData.email.trim()[0] === "+" ? "phoneNumber" : "email";
      let resp = await fetchVerifyCredentials({
        variables: {
          [userMethod]: formData.email.trim(),
        },
      });
      data = resp.data.verifyCredentials;
      setPage(
        Routes.verify_credentials({
          bodyProps: {
            sessionToken: data.sessionToken,
            input: {
              ...formData,
            },
          },
        })
      );
    } catch (e) {
      pushToast("Something went wrong. Please try again.", "failed");
    }
  };
  return (
    <>
      <section className="Auth-card">
        <div className="Auth-card__title">Sign up</div>
        <form className="form" onSubmit={handleSubmit}>
          <div className="input-row">
            <InputText
              inputProps={{
                id: "firstName",
                name: "firstName",
                placeholder: "First Name",
                value: formData.firstName,
                onChange: handleChange,
              }}
              classes={{
                input_rightLogo_classes: errors.firstName
                  ? "input__right-logo--error"
                  : "",
                input_classes: `${errors.firstName ? "input--error" : ""}`,
                input_component_Wrapper_classes: errors.firstName
                  ? "mg-bt-0"
                  : "",
              }}
              elements={{
                rightLogo: <Icons8User width="15px" height="15.683px" />,
                inputMessage: errors.firstName ? (
                  <div className="error-message">{errors.firstName}</div>
                ) : (
                  ""
                ),
                inputLabel: (
                  <label htmlFor="firstName" className="input__label">
                    {"First Name"}
                  </label>
                ),
              }}
            />
            <InputText
              inputProps={{
                id: "lastName",
                name: "lastName",
                placeholder: "Last Name",
                value: formData.lastName,
                onChange: handleChange,
              }}
              classes={{
                input_rightLogo_classes: errors.lastName
                  ? `input__right-logo--error`
                  : "",
                input_classes: `${errors.lastName ? "input--error" : ""}`,
                input_component_Wrapper_classes: errors.lastName
                  ? "mg-bt-0"
                  : "",
              }}
              elements={{
                rightLogo: <Icons8User width="15px" height="15.683px" />,
                inputMessage: errors.lastName ? (
                  <div className="error-message">{errors.lastName}</div>
                ) : (
                  ""
                ),
                inputLabel: (
                  <label htmlFor="lastName" className="input__label ">
                    {"Last Name"}
                  </label>
                ),
              }}
            />
          </div>
          <InputText
            inputProps={{
              id: "email",
              name: "email",
              placeholder: "Mobile / Email",
              value: formData.email,
              onChange: handleChange,
            }}
            classes={{
              input_rightLogo_classes: errors.email
                ? `input__right-logo--error`
                : "",
              input_classes: `${errors.email ? "input--error" : ""}`,
              input_component_Wrapper_classes: errors.email ? "mg-bt-0" : "",
            }}
            elements={{
              rightLogo: <Icons8User width="15px" height="15.683px" />,
              inputMessage: errors.email ? (
                <div className="error-message">{errors.email}</div>
              ) : (
                ""
              ),
              inputLabel: (
                <label htmlFor="email" className="input__label">
                  {"Mobile / Email"}
                </label>
              ),
            }}
          />
          <InputPassword
            inputProps={{
              autocomplete: "off",
              id: "pass",
              name: "password",
              placeholder: "Password",
              value: formData.password,
              onChange: handleChange,
            }}
            classes={{
              input_rightLogo_classes: errors.password?.length
                ? `input__right-logo--error`
                : "",
              input_leftLogo_classes: errors.password?.length
                ? `input__right-logo--error`
                : "custom-logo",
              input_classes: `${errors.password?.length ? "input--error" : ""}`,
            }}
            elements={{
              hideLogo: (
                <Icons8HiddenEye width={"20.648px"} height="13.766px" />
              ),
              showLogo: <Icons8Eye width="20.648px" height="13.766px" />,
              rightLogo: <Icons8Password width="15px" height="15.683px" />,
              inputMessage: errors?.password?.length ? (
                <div className="error-message">
                  {errors.password.map((error) => (
                    <div>{`* ${error}.`}</div>
                  ))}
                </div>
              ) : (
                ""
              ),
              inputLabel: (
                <label htmlFor={"pass"} className="input__label">
                  {"Password"}
                </label>
              ),
            }}
          />
          <InputPassword
            inputProps={{
              autocomplete: "off",
              id: "confPass",
              name: "confirmPassword",
              placeholder: "Confirm Password",
              value: formData.confirmPassword,
              onChange: handleChange,
            }}
            classes={{
              input_rightLogo_classes: errors.confirmPassword
                ? `input__right-logo--error`
                : "",
              input_leftLogo_classes: errors.confirmPassword
                ? `input__right-logo--error`
                : "custom-logo",
              input_classes: `${errors.confirmPassword ? "input--error" : ""}`,
              input_component_Wrapper_classes: errors.confirmPassword
                ? "mg-bt-0"
                : "",
            }}
            elements={{
              hideLogo: (
                <Icons8HiddenEye width={"20.648px"} height="13.766px" />
              ),
              showLogo: <Icons8Eye width="20.648px" height="13.766px" />,
              rightLogo: <Icons8Password width="15px" height="15.683px" />,
              inputMessage: errors.confirmPassword ? (
                <div className="error-message">{errors.confirmPassword}</div>
              ) : (
                ""
              ),
              inputLabel: (
                <label htmlFor={"confPass"} className="input__label">
                  {"Confirm Password"}
                </label>
              ),
            }}
          />
          <InputText
            inputProps={{
              id: "dateOfBirth",
              name: "dateOfBirth",
              type: "date",
              max: today_date_string_format(),
              value: formData.dateOfBirth,
              onChange: handleChange,
            }}
            classes={{
              input_classes: `input-date ${
                errors.dateOfBirth ? "input--error" : ""
              }`,
              input_rightLogo_classes: `input__right-logo--error`,
              input_component_Wrapper_classes: errors.dateOfBirth
                ? "mg-bt-0"
                : "",
            }}
            elements={{
              inputMessage: errors.dateOfBirth ? (
                <div className="error-message">{errors.dateOfBirth}</div>
              ) : (
                ""
              ),
              inputLabel: (
                <label htmlFor="dateOfBirth" className="input__label">
                  {"Date of Birth"}
                </label>
              ),
            }}
          />
          <div
            className={`Input-component-Wrapper ${
              errors.gender ? "mg-bt-0" : ""
            }`}
          >
            <label htmlFor="gender" className="input__label">
              {"Gender"}
            </label>
            <div className="input-row">
              <InputRadio
                inputProps={{
                  name: "gender",
                  value: "Male",
                  checked: formData.gender === "Male",
                  onChange: handleChange,
                  id: "gender",
                }}
                wrapperProps={{
                  onKeyDown: (e) => {
                    if (e.key === "Enter") {
                      handelGenderFocus("Male");
                    }
                  },
                  tabIndex: 0,
                }}
                classes={{
                  input_classes: "radio",
                  input_Wrapper_classes: `radio-wrapper ${
                    formData.gender === "Male" ? "selected_input" : ""
                  } ${errors.gender ? "input--error" : "sas"}`,
                }}
              />

              <InputRadio
                inputProps={{
                  name: "gender",
                  value: "Female",
                  checked: formData.gender === "Female",
                  onChange: handleChange,
                  id: "Female",
                }}
                classes={{
                  input_Wrapper_classes: `radio-wrapper ${
                    formData.gender === "Female" ? "selected_input" : ""
                  } ${errors.gender ? "input--error" : ""}`,
                }}
                // elements={{
                //     "inputMessage": errors.lastName ? <div className='error-message'>{errors.lastName}</div> : "",
                //     "inputLabel": <div className='input__label'>{"First Name"}</div>,

                // }}
                wrapperProps={{
                  onKeyDown: (e) => {
                    if (e.key === "Enter") {
                      handelGenderFocus("Female");
                    }
                  },
                  tabIndex: 0,
                }}
              />
            </div>
            <div className="error-message">
              {errors.gender ? (
                <div className="error-message">{errors.gender}</div>
              ) : (
                ""
              )}
            </div>
          </div>
          <p className="terms-condtions">
            <InputCheckbox
              inputProps={{
                name: "termsAccepted",
                checked: formData.termsAccepted,
                onChange: handleChange,
                id: "terms",
              }}
              classes={{
                input_classes: "checkbox",
                input_Wrapper_classes: "checkbox-wrapper book",
                checkbox: "checkbox-label",
              }}
              wrapperProps={{
                onKeyDown: (e) => {
                  if (e.key === "Enter") {
                    setFormData((prev) => {
                      return {
                        ...prev,
                        termsAccepted: !prev.termsAccepted,
                      };
                    });
                  }
                },
                tabIndex: 0,
              }}
              elements={{
                label: "I have read and accepted the ",
                clickablePart: (
                  <a
                    href="#"
                    className="auth__link terms_condtion decoration book"
                    role="button"
                    onClick={() => setPage(Routes.terms)}
                  >
                    Terms of Use & Privacy Policy.
                  </a>
                ),
              }}
            />
          </p>
          <button
            className={`Auth-card__button ${
              isButtonDisable ? "Auth-card__button--disable" : ""
            }`}
            type="submit"
          >
            Sign up
          </button>
          {errors.termsAccepted ? (
            <div className="error-message error-login mg-tp-5">
              {errors.termsAccepted}
            </div>
          ) : (
            ""
          )}
        </form>
      </section>

      <SocialAuthProviderOptions
        hrefs={social?.providers?.map((e: { loginUrl: string }) => e.loginUrl)}
      />
      <div className="auth-container">
        Already have an account?{" "}
        <a
          href="#"
          tabIndex={0}
          className="auth__link"
          role="button"
          onClick={() => setPage(Routes.sign_in)}
        >
          Sign in
        </a>
      </div>
      <img
        src={IsoLogo}
        alt="Button Image"
        style={{ width: "250px", height: "60px", marginTop: "10px" }}
      />
    </>
  );
};

export default SignUp;
