// This is a copy paste from https://github.com/InseeFrLab/keycloakify/blob/main/src/lib/pages/Login.tsx

// You can replace all relative imports by cherry picking files from the keycloakify module.
// For example, the following import:
// import { clsx } from "./tools/clsx";
// becomes:
import type { PageProps } from "keycloakify/lib/KcProps";
// Here use your own KcContext and I18n that you might have overloaded.
import type { KcContext } from "../kcContext";
import type { I18n } from "../i18n";
import "./app.css";

import { usePageContext } from "keycloak-theme/context/PageContext";

import { LoginContext } from "keycloak-theme/context/LoginContext";
import { config } from "../../ApplicationConfig";
import { ReactNode, useEffect, useState } from "react";

export default function Login(
  props: PageProps<Extract<KcContext, { pageId: "login.ftl" }>, I18n>
) {
  const { page } = usePageContext();
  const { kcContext } = props;

  const { url } = kcContext;
  const currentUrl = url.loginUrl;
  const match = currentUrl.match(/client_id=([^\&]+)/);
  let clientId: string = "";
  if (match) {
    clientId = match[1].toLowerCase();
  } else {
    clientId = "any";
  }
  // clientId = "admin";
  const user =
    config.users.find((e) => e.client_id_in_URL === clientId) ||
    config.users.find((e) => e.type === "any");
  const [isNotStart, setIsNotStart] = useState(false);
  useEffect(() => {
    document.title = user?.app_name || "";
  }, []);
  const [pageStack, setPageStack] = useState<ReactNode[]>([<div></div>]);
  function addNewPage(page: ReactNode) {
    setPageStack((pages) => [...pages, page]);
  }
  const [shouldRotate, setShouldRotate] = useState(false);
  useEffect(() => {
    var element1 = document.getElementById("app");
    if (element1) {
      element1.scrollTop = 0;
    }
    var front = document.getElementById("front");
    if (front) {
      front.scrollTop = 0;
    }

    if (window.innerWidth > 472 && isNotStart) {
      setShouldRotate((prev) => true);
      addNewPage(page);
      setTimeout(() => {
        setShouldRotate((prev) => false);
      }, 900);
    } else {
      setIsNotStart(true);
      addNewPage(page);
    }
  }, [page]);
  return (
    <LoginContext.Provider value={{ ...props, user }}>
      <div className={`card ${shouldRotate ? "custom-rotate" : ""}`}>
        <div id="front" className={`card-side front`}>
          {pageStack[pageStack.length - 1]}
        </div>
        <div
          id="back"
          className={`card-side back ${shouldRotate ? "" : "display-none"}`}
        >
          {pageStack[pageStack.length - 2]}
        </div>
        <div className={`display-none`}>{pageStack[pageStack.length - 1]}</div>
      </div>
    </LoginContext.Provider>
  );
}
