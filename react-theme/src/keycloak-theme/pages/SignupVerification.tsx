import { useMutation } from "@apollo/client";
import { config } from "ApplicationConfig";
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { useEffect, useState } from "react";
import {
  CONFIRM_VERIFICATION,
  CUSTOMER_CREATE,
  VERIFY_CREDENTIALS,
} from "../GraphQL/Mutations";
import { useToastContext } from "../Providers/ToastProvider";
import OtpForm from "../components/OtpForm";
import { usePageContext } from "../context/PageContext";

interface SignupVerificationProps {
  input: {
    firstName: string;
    lastName: string;
    email: string;
    dateOfBirth: string;
    password: string;
    gender: string;
  };
  sessionToken: string;
}
export const defaultPropsForRoute = {
  input: {
    firstName: "",
    lastName: "",
    email: "",
    dateOfBirth: "",
    password: "",
    gender: "",
  },
  sessionToken: "",
};

export default function SignupVerification(props: SignupVerificationProps) {
  const { setAppClass } = useAppClass();
  const { pushToast } = useToastContext();
  const { setPage, Routes } = usePageContext();
  const [error, setError] = useState("");
  const [sessionToken, setSessionToken] = useState(props.sessionToken);
  const [fetchVerifyCredentials] = useMutation(VERIFY_CREDENTIALS);
  const [fetchConfirmVerification] = useMutation(CONFIRM_VERIFICATION);
  const [fetchCustomerCreate] = useMutation(CUSTOMER_CREATE);

  const handelResend = async function () {
    let userMethod =
      props.input.email.trim()[0] === "+" ? "phoneNumber" : "email";
    try {
      let resp = await fetchVerifyCredentials({
        variables: {
          [userMethod]: props.input.email.trim(),
        },
      });
      let data = resp.data.verifyCredentials;
      setSessionToken(data.sessionToken);
    } catch (e) {
      pushToast("Something went wrong. Please try again.", "failed");
    }
  };

  const handelVerify = async function (otp: string) {
    let userMethod =
      props.input.email.trim()[0] === "+" ? "phoneNumber" : "email";
    try {
      let response = await fetchConfirmVerification({
        variables: {
          [userMethod]: props.input.email.trim(),
          sessionToken: sessionToken,
          verificationCode: otp,
        },
      });
      let status = response.data.confirmVerification.success;
      if (!status) {
        setError(
          response.data.confirmVerification.accountErrors.message ||
            "verification_code is not valid."
        );
        return;
      }
      userMethod = props.input.email.trim()[0] === "+" ? "mobile" : "email";
      let TokenType =
        props.input.email.trim()[0] === "+"
          ? "mobileVerificationToken"
          : "emailVerificationToken";
      let Object = {
        [userMethod]: props.input.email.trim(),
        [TokenType]: sessionToken,
      };
      let resp = await fetchCustomerCreate({
        variables: {
          input: {
            firstName: props.input.firstName,
            lastName: props.input.lastName,
            dateOfBirth: props.input.dateOfBirth,
            password: props.input.password,
            gender: props.input.gender === "Male" ? "MALE" : "FEMALE",
            ...Object,
          },
        },
      });
      if (resp.data.customerCreate.patientErrors.length) {
        pushToast("Something went wrong. Please try again.", "failed");
        return;
      } else {
        setPage(Routes.sign_in);
        pushToast("Account Created Successfully", "success");
      }
    } catch (e) {
      pushToast("Something went wrong. Please try again.", "failed");
    }
  };

  useEffect(() => {
    setAppClass("app-md");
  }, []);

  return (
    <OtpForm
      seconds={config.RESEND_OTP_FOR_FORGETPASSWORD_IN_SECOUND}
      onSubmit={handelVerify}
      onResend={handelResend}
      message={`Please enter the 4-digit verification code that was sent to`}
      username={props.input.email.trim() ?? "your account"}
      outterError={error}
    />
  );
}
