import React, { useEffect } from 'react';
import { useAppClass } from 'keycloak-theme/context/AppClassContext';
import { usePageContext } from '../context/PageContext';

interface TermsProps {}

const Terms: React.FC<TermsProps> = ({}) => {
    const { setPage, Routes } = usePageContext();
    const { setAppClass } = useAppClass();

    useEffect(() => {
        setAppClass("app-lg");
    }, []);

    return (
        <div className="terms">
            <p className="book">The purpose of this form is to obtain your informed consent to participate in a virtual call/ consultation with ioHealth medical professionals.</p>
            <p className="book">In preparation for the call, the medical professional will be able to view the consumer’s medical record declared history, and other medical tests shared by the consumer, all of which might be discussed with the medical during real-time audio and video calls. The medical professional may ask the consumer questions during the call, as an alternative to the in-person medical examination. For the consumers’ safety and quality assurance regulatory purposes, this call may be recorded and safely stored. The management, storage, and processing of any medical information for the consumer are all regulator compliant to protect consumer privacy.</p>
            <p className="book">Sehacity virtual visits are similar in nature to those in-person medical visits. Technical difficulties may arise due to the remote nature of the call. To address this, sehacity’s protocol will make sure that the consumer receives the care they need including the possibility of making direct mobile calls on the provided phone number when such issues arise.</p>
            <p className="medium">I have read the information above and hereby I declare that I give my consent to this sehacity call with full knowledge and capacity and I was not coerced, harassed, or compelled in any way to sign this informed consent.</p>
            <p className="medium">The consumer is above the minimum age for consent</p>
            <div className="terms__Done">
                <button className="Auth-card__button terms__button" onClick={() => setPage(Routes.sign_up)}>Done</button>
            </div>
        </div>
    );
}

export default Terms;
