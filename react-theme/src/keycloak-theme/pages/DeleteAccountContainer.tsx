import { useAppClass } from "keycloak-theme/context/AppClassContext";
import { ReactNode, useEffect, useState } from "react";
import { usePageContext } from "../context/PageContext";
import { Routes } from "keycloak-theme/utilities/Routes";
import { LoginContext } from "keycloak-theme/context/LoginContext";
import { config } from "ApplicationConfig";
import { PageProps } from "keycloakify";
import { KcContext } from '../kcContext';
import { I18n } from '../i18n';

export default function DeleteAccountRootPage(props: PageProps<Extract<KcContext, { pageId: "error.ftl" }>, I18n>) {
  const { page } = usePageContext();
  const user = config.users.find((e) => e.type === "any");
  const { setAppClass } = useAppClass();
  const [shouldRotate, setShouldRotate] = useState(false);
  const [isNotStart, setIsNotStart] = useState(false);
  const [pageStack, setPageStack] = useState<ReactNode[]>([
    Routes.delete_account({}),
  ]);
  
  useEffect(() => {
    setAppClass("app-md");
  }, []);

  useEffect(() => {
    var element1 = document.getElementById("app");
    if (element1) {
      element1.scrollTop = 0;
    }
    var front = document.getElementById("front");
    if (front) {
      front.scrollTop = 0;
    }

    if (window.innerWidth > 472 && isNotStart) {
      setShouldRotate((prev) => true);
      addNewPage(page);
      setTimeout(() => {
        setShouldRotate((prev) => false);
      }, 900);
    } else {
      setIsNotStart(true);
      addNewPage(page);
    }
  }, [page]);

  function addNewPage(page: ReactNode) {
    let stack = [...pageStack];
    stack.push(page);
    setPageStack(stack);
  }

  return (
    <LoginContext.Provider value={{ ...props, user }}>
    <div className={`card  ${shouldRotate ? "custom-rotate" : ""}  `}>
      <div id="front" className={`card-side front`}>
        {pageStack[pageStack.length - 1]}
      </div>
      <div
        id="back"
        className={`card-side back ${shouldRotate ? "" : "display-none"}`}
      >
        {pageStack[pageStack.length - 2]}
      </div>
      <div className={`display-none`}>{pageStack[pageStack.length - 1]}</div>
    </div>
    </LoginContext.Provider>
  );
}
