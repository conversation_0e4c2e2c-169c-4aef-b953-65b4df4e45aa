import type { Kc<PERSON>ontext } from "../kcContext";
import type { I18n } from "../i18n";
import { useEffect } from 'react';
import { useAppClass } from "keycloak-theme/context/AppClassContext";
import type { PageProps } from "keycloakify";
import IconPlant from "keycloak-theme/components/logo/IconPlant";
export default function LoginPageExpired(props: PageProps<Extract<KcContext, { pageId: "login-page-expired.ftl" }>, I18n>) {
    const { kcContext, i18n } = props;

    const { url } = kcContext;

    const { msg } = i18n;
    const {setAppClass } = useAppClass();

    useEffect(()=>{
        setAppClass("app-sm")        
    },[])
    return (
        <div id="kc-info-message">
        <div><IconPlant width="139px" height="127.483px"/></div>
        <p className="custom-kc-page-title">
            {"Page has expired"}
        </p>
        <br />
        <div className="auth-container page-expired" id="instruction1" >
                {msg("pageExpiredMsg1")}
                <a className="auth__link " id="loginRestartLink" href={url?.loginRestartFlowUrl}>
                {" "}{msg("doClickHere")}
                </a>{" "}
                <br />
        </div>
        <div className="auth-container page-expired" id="instruction1" >
        {msg("pageExpiredMsg2")}{" "}
                <a className="auth__link margin-top-5px" id="loginContinueLink" href={url?.loginAction}>
                    {msg("doClickHere")}
                </a>{" "}
        </div>
    </div>
    );
}