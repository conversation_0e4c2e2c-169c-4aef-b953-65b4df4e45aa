import { FilledInput } from "@mui/material";
import { ReactNode } from "react";
import { bg } from "./theme/colors";

const MyTextField = (props: { startIcon: ReactNode } & any) => {
  const { startIcon, name, label, defaultValue } = props;
  return (
    <>
      <FilledInput
        type="text"
        fullWidth
        startAdornment={startIcon}
        defaultValue={defaultValue}
        name={name}
        placeholder={label}
        inputProps={{
          style: {
            paddingTop: 0,
            paddingBottom: 0,
            backgroundColor: bg + "!important",
            height: "100%",
            width: "calc(100% - 60px)",
            borderTopRightRadius: 30,
            borderBottomRightRadius: 30,
            lineHeight: "100%",
          },
        }}
        sx={{
          background: bg,
          borderRadius: 10,
          height: "55px",
          fontSize: "16px",
          fontWeight: "300",
          paddingTop: 0,
          paddingBottom: 0,
          fontFamily: "'Airbnb Cereal App'",
          width: { xs: "80vw", sm: "425px" },
          backgroundColor: bg + "!important",

          "& input:hover, & input:focus": {
            background: bg,
            backgroundColor: bg + "!important",
          },
          "& input:-webkit-autofill": {
            boxShadow: "inherit!important",
            background: bg + "!important",
          },
        }}
        {...props}
      />
    </>
  );
};
export default MyTextField;
