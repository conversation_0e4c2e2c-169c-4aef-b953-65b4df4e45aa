export const UaePassIcon = () => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="182.396"
      height="53.245"
      viewBox="0 0 182.396 53.245"
    >
      <g id="uae-pass-logo" transform="translate(-14.003 -7.002)">
        <g
          id="Group_8426"
          data-name="Group 8426"
          transform="translate(14.003 7.002)"
        >
          <path
            id="Path_11234"
            data-name="Path 11234"
            d="M57.157,7.27A28.314,28.314,0,0,1,73.342,9.816a7.093,7.093,0,0,1,2.342,1.461,1.494,1.494,0,0,1-.325,1.885c-.606.542-1.4.1-2.01-.179a26.572,26.572,0,0,0-13.157-3.2,24.014,24.014,0,0,0-10.522,3,5.88,5.88,0,0,1-1.618.67,1.423,1.423,0,0,1-.651-2.581,25.172,25.172,0,0,1,9.756-3.6Zm46.49.124c1.184-.852,2.437,1.18,1.168,1.87C103.634,10.061,102.438,8.083,103.647,7.394Zm3.241.1a1.1,1.1,0,1,1-.013,1.646,1.1,1.1,0,0,1,.013-1.646Zm61.835-.128a1.1,1.1,0,1,1-.4,1.528,1.091,1.091,0,0,1,.4-1.528Zm3.248.077a1.1,1.1,0,1,1-.035,1.729A1.1,1.1,0,0,1,171.971,7.442Zm-19.324,1.33h1.723c-.013,3.876.013,7.756-.016,11.632,0,1.445-.6,3.139-2.106,3.611-1.7.431-3.477.16-5.213.233a5.189,5.189,0,0,1-1.34,2.571,5.825,5.825,0,0,1-3.554,1.34V26.542a3.077,3.077,0,0,0,3.245-2.941c.166-3.407.013-6.824.08-10.235h1.7c.022,3.053.006,6.106.01,9.159a26.132,26.132,0,0,0,4.093-.118c1.133-.309,1.381-1.621,1.372-2.635.016-3.666,0-7.331.006-11Zm4.878,0,1.7,0q0,7.733,0,15.473h-1.7c-.019-5.159,0-10.318-.006-15.473Zm54.619,10.662c0-3.554-.01-7.108,0-10.662h1.7V20.1a4.34,4.34,0,0,1-1.241,3.446,4.306,4.306,0,0,1-2.785.705c-5.672.006-11.348-.019-17.02.013a5.089,5.089,0,0,1-1.4,2.619,5.977,5.977,0,0,1-3.477,1.286l-.022-1.621a3.12,3.12,0,0,0,3.168-2.281c-1.688-.1-3.592.29-5.057-.762-3.06-2.233-2.871-7.689.45-9.593,2.09-1.19,5.35-.507,6.157,1.956.67,2.157.223,4.46.351,6.681,1.4.006,2.807-.006,4.211.01a5.45,5.45,0,0,1,.335-6.77,4.326,4.326,0,0,1,6.46.59,5.628,5.628,0,0,1-.274,6.164c1.139-.013,2.278,0,3.42-.01a8.276,8.276,0,0,0-5.414-9.74c0-.59,0-1.177.006-1.767a9.645,9.645,0,0,1,6.63,6,13.894,13.894,0,0,1,.577,5.529c.845-.032,1.831.108,2.514-.517a3.652,3.652,0,0,0,.711-2.6m-25.118-3.956a4.552,4.552,0,0,0-.134,6.476c1.2,1.015,2.887.447,4.313.574-.032-1.866.073-3.733-.051-5.6a2.6,2.6,0,0,0-4.128-1.455m12.729.673c-1.946.66-2.134,3.3-1.461,4.945a2.423,2.423,0,0,0,4.237.332,5.36,5.36,0,0,0,.131-4.09,2.335,2.335,0,0,0-2.9-1.187Zm17.266-7.379,1.691,0c0,5.156.01,10.314-.01,15.473h-1.678q-.01-7.738,0-15.473ZM135,9.768a1.1,1.1,0,1,1-.217,1.78A1.107,1.107,0,0,1,135,9.768Zm3.481-.054c1.3-.593,2.163,1.6.81,2.035C137.978,12.358,137.142,10.163,138.476,9.713ZM104.2,11.127c1.723-.022,3.446.006,5.168,0,.016,2.68-.029,5.36.019,8.033a3.289,3.289,0,0,0,2,3.232,3.379,3.379,0,0,0,2.807-.287,3,3,0,0,0,.769-2.31c.022-2.131,0-4.262.006-6.393l1.726.006c-.006,2.22.006,4.441,0,6.661a8.212,8.212,0,0,1-.4,2.466c.976-.006,1.952,0,2.935,0a6.872,6.872,0,0,1,.711-4.9c1.681-2.862,6.483-2.929,8.247-.121a6.861,6.861,0,0,1,.721,5.022c1.458-.01,2.919-.013,4.38,0a6.467,6.467,0,0,1-.064-7.236c1.9-2.667,6.6-2.54,8.19.383,1.665,2.916.606,7.66-3.021,8.413-3.4.335-6.837.054-10.251.16a5.067,5.067,0,0,1-8.151.01c-2.827-.086-5.66.073-8.483-.083a4.4,4.4,0,0,1-3.854-3.337,4.146,4.146,0,0,1-6.476.357,7.377,7.377,0,0,1-.447-8.1A4.1,4.1,0,0,1,104.2,11.13Zm-1.448,2.051a5.812,5.812,0,0,0-.823,6.03,2.9,2.9,0,0,0,3.248,1.972,3.035,3.035,0,0,0,2.243-2.67,41.5,41.5,0,0,0,.2-6.109c-1.608.16-3.573-.5-4.865.778m33.154,1.93a3.383,3.383,0,0,0-1.742,2.734A5.146,5.146,0,0,0,135,21.856a2.967,2.967,0,0,0,4.817-.613,6.451,6.451,0,0,0,.035-4.849,2.943,2.943,0,0,0-3.943-1.286m-12.713,2.029a3.112,3.112,0,0,0-1.988,2.591,5.506,5.506,0,0,0,.549,3.854,2.988,2.988,0,0,0,3.991.734,3.481,3.481,0,0,0,1.289-2.61,5.169,5.169,0,0,0-.81-3.857A2.857,2.857,0,0,0,123.191,17.137Zm41.994-2.492a4.475,4.475,0,0,1,3.774-3.493c1.844-.077,3.694-.006,5.542-.029.013,2.664-.022,5.331.013,7.995a3.307,3.307,0,0,0,2.029,3.28c1.254.367,3.222.242,3.541-1.33.386-2.54.089-5.13.191-7.689.558-.01,1.123-.013,1.691-.019,0,2.428.045,4.856-.022,7.284A3.5,3.5,0,0,1,179.9,24a8.7,8.7,0,0,1-4.1.032,4.527,4.527,0,0,1-3.015-3.178,4.174,4.174,0,0,1-6.2.654,7.2,7.2,0,0,1-1.4-6.866m1.582.632a5.572,5.572,0,0,0,1.34,5.373,2.649,2.649,0,0,0,4.128-1.11c.861-2.278.386-4.766.5-7.14-1.158.026-2.323-.073-3.477.054A3.449,3.449,0,0,0,166.768,15.277Z"
            transform="translate(-36.319 -7.002)"
            fill="#1d1c1b"
          />
          <path
            id="Path_11235"
            data-name="Path 11235"
            d="M45.581,26.934a5.175,5.175,0,0,1,1.857-.156A24.027,24.027,0,0,1,61.7,31.4a26,26,0,0,1,5.507,5.653,1.409,1.409,0,0,1-.935,2.182c-.9.182-1.388-.7-1.86-1.286A21.6,21.6,0,0,0,46.443,29.6,1.43,1.43,0,0,1,45.581,26.934Zm-5.35.756a1.4,1.4,0,0,1,1.959,1.685c-.313,1-1.522.992-2.339,1.33-4.4,1.378-7.938,4.658-10.474,8.413-.437.619-.893,1.378-1.72,1.493A1.5,1.5,0,0,1,26.4,38.368a30.464,30.464,0,0,1,6.342-6.757,27.614,27.614,0,0,1,7.488-3.921ZM29.052,27.9a1.4,1.4,0,1,1,.207,2.421,1.457,1.457,0,0,1-.211-2.421Z"
            transform="translate(-22.29 -20.465)"
            fill="#1d1c1b"
          />
          <path
            id="Path_11236"
            data-name="Path 11236"
            d="M49.349,45.3a18.868,18.868,0,0,1,14.42,3.656,17.4,17.4,0,0,1,6.8,13.007c.077,2.565-.045,5.3-1.35,7.574a5.455,5.455,0,0,1-5.353,2.632,1.442,1.442,0,0,1-.4-2.635c.845-.316,1.834-.108,2.626-.606,1.445-.817,1.93-2.581,2.048-4.125a16.221,16.221,0,0,0-3.165-10.994,14.885,14.885,0,0,0-8.008-5.136,17.363,17.363,0,0,0-13.186,1.477,16.761,16.761,0,0,0-7.928,12.414,22.735,22.735,0,0,0,.833,9.258,1.586,1.586,0,0,1-.389,1.857,1.362,1.362,0,0,1-2.169-.766c-1.5-5.232-1.71-11.007.367-16.118C36.91,50.652,42.923,46.367,49.349,45.3Z"
            transform="translate(-26.918 -32.927)"
            fill="#1d1c1b"
          />
          <path
            id="Path_11237"
            data-name="Path 11237"
            d="M39.886,64.9a1.378,1.378,0,0,1,2.109.865A1.406,1.406,0,0,1,40.115,67.4,1.455,1.455,0,0,1,39.886,64.9Zm49.482.338a1.1,1.1,0,0,1,1.85.8,1.1,1.1,0,1,1-1.85-.8Zm3.452-.067a1.1,1.1,0,1,1,.016,1.751A1.105,1.105,0,0,1,92.82,65.168Zm61.963.054a1.1,1.1,0,1,1,.182,1.758,1.1,1.1,0,0,1-.182-1.758Zm3.455-.061a1.1,1.1,0,1,1-.3,1.417A1.051,1.051,0,0,1,158.237,65.161ZM45,66.779a1.356,1.356,0,0,1,1.633.07,14.567,14.567,0,0,1,5.373,9.4,5.648,5.648,0,0,1-.07,2.552,1.342,1.342,0,0,1-2.476-.606,13.543,13.543,0,0,0-4.489-9.029A1.467,1.467,0,0,1,45,66.779Zm15,.364a1.4,1.4,0,1,1-.041,2.39A1.455,1.455,0,0,1,60,67.143ZM15.932,68.671a1.362,1.362,0,0,1,1.857.606c.364.619-.022,1.292-.182,1.908a20.544,20.544,0,0,0-.893,5.108,1.347,1.347,0,0,1-1.774,1.1,1.391,1.391,0,0,1-.932-1.436,22.792,22.792,0,0,1,1.069-5.972,1.982,1.982,0,0,1,.855-1.308Zm20.8,2.38a4.338,4.338,0,0,1,2.374-.236,1.428,1.428,0,0,1,.242,2.463c-.785.367-1.716.278-2.479.718a4.691,4.691,0,0,0-2.4,4.192A16.161,16.161,0,0,0,38.81,89.363a16.717,16.717,0,0,0,6.917,4.281,1.447,1.447,0,0,1,1.2,1.745,1.312,1.312,0,0,1-1.729.967c-3.8-.721-7.019-3.267-9.354-6.259-2.782-3.627-4.578-8.244-4.071-12.867a7.292,7.292,0,0,1,4.958-6.18Zm25.5.558a1.408,1.408,0,1,1-.172,2.565A1.457,1.457,0,0,1,62.231,71.609ZM42.757,72.837c.916-.581,1.885.236,2.2,1.094A10.531,10.531,0,0,1,45.9,76.88a1.463,1.463,0,0,1-2.61.909c-.453-.989-.67-2.08-1.085-3.088a1.424,1.424,0,0,1,.555-1.866Z"
            transform="translate(-14.003 -46.263)"
            fill="#1d1c1b"
          />
          <path
            id="Path_11238"
            data-name="Path 11238"
            d="M88.771,102.945a1.243,1.243,0,0,1,1.956.861,13.542,13.542,0,0,0,1.378,5.449,9.766,9.766,0,0,0,8.69,5.175c.766.057,1.739-.274,2.31.418a1.438,1.438,0,0,1-.721,2.339,12.558,12.558,0,0,1-12.324-5.918,12.94,12.94,0,0,1-1.93-6.54,2,2,0,0,1,.641-1.787Zm39.021,3.529h1.946c0,4.422-.01,8.85,0,13.278a3.646,3.646,0,0,0,2.434,3.321,6.488,6.488,0,0,0,5.637-.412,3.376,3.376,0,0,0,1.7-2.935c.019-4.419,0-8.837.006-13.256l1.927,0v13.256a5.654,5.654,0,0,1-4.154,5.309,8.088,8.088,0,0,1-8.135-1.777,5.031,5.031,0,0,1-1.359-3.516C127.786,115.323,127.8,110.9,127.792,106.473Zm23.8-.281,1.468.01q3.766,9.523,7.549,19.033l-2.055,0c-.775-1.927-1.576-3.848-2.351-5.775q-3.862-.024-7.727,0c-.8,1.921-1.611,3.838-2.367,5.778h-2.071q3.79-9.518,7.555-19.043m.778,2.674c-1.082,2.935-2.106,5.883-3.158,8.824h6.221C154.422,114.749,153.372,111.82,152.374,108.866Zm10.72-2.4q5.527,0,11.061,0l0,1.927h-9.118v6.48l8.933,0c0,.641-.006,1.283,0,1.924-2.98-.006-5.956,0-8.93,0-.006,2.173,0,4.352,0,6.527h9.166l0,1.9H163.093l0-18.766Zm21.969-.016c2.418.019,4.84-.026,7.261.016a5.466,5.466,0,0,1,4.642,2.9c1.653,2.989.664,7.469-2.661,8.837-2.329.845-4.875.3-7.3.453-.006,2.189,0,4.38,0,6.572q-.971,0-1.943,0V106.454M187,108.4q.01,4.2,0,8.391c1.675-.013,3.353.022,5.028-.013a3.912,3.912,0,0,0,3.35-2.042c1.155-2.211.211-5.561-2.383-6.2a52.921,52.921,0,0,0-6-.137Zm10.889,16.835q3.781-9.523,7.552-19.04H206.9q3.781,9.523,7.552,19.037l-2.042,0c-.788-1.927-1.579-3.851-2.358-5.775q-3.867-.024-7.73-.006c-.8,1.924-1.611,3.841-2.367,5.781h-2.064m5.168-7.545q3.111,0,6.221,0c-1.03-2.941-2.029-5.889-3.088-8.818-1.034,2.945-2.086,5.88-3.136,8.818Zm15.017-9.571a7.711,7.711,0,0,1,9.38-.542,5.4,5.4,0,0,1,1.914,2.945c-.638.026-1.27.051-1.9.067-1.334-2.919-5.6-3.181-7.9-1.3-1.544,1.18-1.452,4.013.421,4.824,2.584,1.094,5.685.485,8.1,2.122,2.364,1.436,2.431,5.063.577,6.945-2.788,2.881-8.046,3.063-10.869.137a5.383,5.383,0,0,1-1.381-2.83l1.94-.051c1.053,3.535,6.192,3.975,8.652,1.691,1.359-1.142,1.436-3.717-.271-4.562-2.578-1.219-5.717-.453-8.183-2.026-2.52-1.531-2.584-5.507-.472-7.414Zm16.676.089a7.7,7.7,0,0,1,9.172-.829,5.346,5.346,0,0,1,2.2,3.139c-.632.032-1.26.054-1.882.08-1.372-2.951-5.717-3.206-8.008-1.238-1.493,1.235-1.3,4.052.593,4.792,2.6,1.027,5.711.459,8.1,2.141,2.169,1.4,2.316,4.734.705,6.623A7.986,7.986,0,0,1,237.218,125a5.766,5.766,0,0,1-4.052-4.514,18.246,18.246,0,0,1,1.914-.022c1.2,3.663,6.732,4.01,9.035,1.257,1.043-1.244.865-3.506-.724-4.211-2.53-1.117-5.538-.447-7.966-1.9-2.6-1.436-2.76-5.449-.68-7.4Z"
            transform="translate(-64.481 -72.19)"
            fill="#1d1c1b"
          />
          <path
            id="Path_11239"
            data-name="Path 11239"
            d="M65.008,114.98A1.414,1.414,0,0,1,66.8,116.8a1.387,1.387,0,0,1-2.169.664A1.468,1.468,0,0,1,65,114.98Zm9.954,5.819a1.4,1.4,0,1,1-.048,2.377,1.459,1.459,0,0,1,.045-2.377ZM42.72,123.964A1.406,1.406,0,1,1,42.7,126.4a1.464,1.464,0,0,1,.019-2.441Z"
            transform="translate(-33.105 -80.458)"
            fill="#1d1c1b"
          />
        </g>
        <path
          id="Path_11240"
          data-name="Path 11240"
          d="M59.975,66.224c.568-.191,1.3-.373,1.78.105a1.408,1.408,0,0,1-.466,2.393,10.869,10.869,0,0,0-6.837,6.3,13.949,13.949,0,0,0-.6,7,21.339,21.339,0,0,0,2.6,7.784,1.391,1.391,0,0,1-1.417,2.077c-1.1-.242-1.324-1.538-1.8-2.39-1.745-4.387-3.079-9.3-1.809-13.993a13.3,13.3,0,0,1,8.537-9.278Z"
          transform="translate(-25.149 -40.192)"
          fill="#1eab75"
        />
        <path
          id="Path_11241"
          data-name="Path 11241"
          d="M70.054,151.734a1.414,1.414,0,0,1,1.978.319,26.066,26.066,0,0,0,3.27,2.8,1.319,1.319,0,0,1-.932,2.351,10.794,10.794,0,0,1-4.466-3.318A1.479,1.479,0,0,1,70.054,151.734Z"
          transform="translate(-37.8 -98.368)"
          fill="#e82026"
        />
      </g>
    </svg>
  );
};
