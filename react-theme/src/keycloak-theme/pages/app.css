.app__container {
  /* background-color: #2cc695; */
  min-height: 480px;
  margin-top: 50px;
  max-width: 600px;
  height: calc(100% - 80px);
  padding: 0px 52px;
}
.flex {
  display: flex;
  justify-content: center;
  align-items: center;
}
.keycloack-message__container {
  /* background-color: #AB403B; */
  flex-direction: column;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  max-width: 600px;
  padding: 0px 52px;
}
/* Animations */

@keyframes decrease-loade-time {
  0% {
    width: 100%;
  }

  100% {
    width: 0%;
  }
}
@keyframes fade-in-from-left {
  0% {
    display: block;
    transform: translateX(0px);
  }

  100% {
    transform: translateX(520px);
    display: none;
    visibility: hidden;
  }
}
@keyframes fade-in {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes make-smaller {
  0% {
    height: 100%;
    width: 100%;
  }

  100% {
    height: 75%;
    width: 78%;
  }
}

/* STYLES */
.header {
  /* background-color: #913263; */
  height: var(--nav_bar_width, 55px);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}
.header__logo {
  position: absolute;
  left: 25px;
  margin-top: 3px;
  cursor: pointer;
}
.header__title {
  font-size: 16px;
  font-family: var(--main-Medium);
  text-align: center;
}

.welcome__text {
  font-size: 18px;
  font-family: var(--main-Medium);
}
.first-heading {
  font-size: 24px;
  font-family: var(--main-Bold);
  margin-top: 15px;
}
.Auth-card {
  padding-bottom: 50px;
  width: 100%;
}
.Auth-card__title {
  font-family: var(--main-Bold);
  font-size: 20px;
  margin-top: 40px;
}
.Auth-card__link {
  margin-right: auto;
  font-size: 14px;
  display: block;
  margin-top: 5px;
  font-family: var(--main-Book);
  text-align: start;
  cursor: pointer;
}

.Auth-options__title {
  font-size: 18px;
  font-family: var(--main-Medium);
}
.Auth-card__button {
  color: var(--white);
  height: 48px;
  width: 100%;
  background: rgb(37, 40, 127);
  background: linear-gradient(
    350deg,
    rgba(37, 40, 127, 1) 0%,
    rgba(97, 38, 103, 1) 0%,
    rgba(99, 38, 102, 1) 0%,
    rgba(71, 39, 114, 1) 0%,
    rgba(86, 39, 108, 1) 0%,
    rgba(101, 38, 102, 1) 37%,
    rgba(114, 38, 97, 1) 100%,
    rgba(136, 37, 88, 1) 100%
  );
  border-radius: 28px;
  margin-top: 40px;
  font-size: 18px;
  opacity: 1;
  cursor: pointer;
}
.Auth-operation__button:hover {
  scale: 1.03;
  background: rgb(136, 37, 88);
  background: linear-gradient(
    170deg,
    rgba(136, 37, 88, 1) 100%,
    rgba(37, 40, 127, 1) 100%
  );
}

.Auth-card__button:focus {
  scale: 1.02;
  background: rgb(136, 37, 88);
  background: linear-gradient(
    170deg,
    rgba(136, 37, 88, 1) 100%,
    rgba(37, 40, 127, 1) 100%
  );
}
.Auth-card__button:hover {
  transform: scale(1.02);
  transition: transform 0.5s ease;
  background: rgb(136, 37, 88);
  background: linear-gradient(
    170deg,
    rgba(136, 37, 88, 1) 100%,
    rgba(37, 40, 127, 1) 100%
  );
}
.Auth-card__button--disable {
  opacity: 0.5;
  background: rgb(37, 40, 127);
  background: linear-gradient(
    350deg,
    rgba(37, 40, 127, 1) 0%,
    rgba(97, 38, 103, 1) 0%,
    rgba(99, 38, 102, 1) 0%,
    rgba(71, 39, 114, 1) 0%,
    rgba(86, 39, 108, 1) 0%,
    rgba(101, 38, 102, 1) 37%,
    rgba(114, 38, 97, 1) 100%,
    rgba(136, 37, 88, 1) 100%
  );
  cursor: none;
}
.Auth-card__button--disable:hover {
  transform: none;
  background: rgb(37, 40, 127);
  background: linear-gradient(
    350deg,
    rgba(37, 40, 127, 1) 0%,
    rgba(97, 38, 103, 1) 0%,
    rgba(99, 38, 102, 1) 0%,
    rgba(71, 39, 114, 1) 0%,
    rgba(86, 39, 108, 1) 0%,
    rgba(101, 38, 102, 1) 37%,
    rgba(114, 38, 97, 1) 100%,
    rgba(136, 37, 88, 1) 100%
  );
  scale: 1;
  cursor: default;
}
.Auth-card__button--disable:focus {
  background: rgb(37, 40, 127);
  background: linear-gradient(
    350deg,
    rgba(37, 40, 127, 1) 0%,
    rgba(97, 38, 103, 1) 0%,
    rgba(99, 38, 102, 1) 0%,
    rgba(71, 39, 114, 1) 0%,
    rgba(86, 39, 108, 1) 0%,
    rgba(101, 38, 102, 1) 37%,
    rgba(114, 38, 97, 1) 100%,
    rgba(136, 37, 88, 1) 100%
  );
  scale: 1;
  cursor: default;
}
/* -------------------- */
.Input-Wrapper {
  width: 100%;
  height: fit-content;
  position: relative;
}
.Input-component-Wrapper {
  font-family: var(--main-Medium);
  font-size: 14px;
  width: 100%;
  margin-bottom: 15px;
  position: relative;
}
.input {
  width: 100%;
  height: 50px;
  background-color: #f2f2ff;
  border-radius: 28px;
  color: var(--main-color);
  text-indent: 41px;
  font-size: 14px;
  font-family: var(--main-Medium);
  color: inherit;
  font-size: inherit;
}
.textAreaInput {
  width: 100%;
  background-color: #f2f2ff;
  border-radius: 22px;
  color: var(--main-color);
  font-size: 14px;
  font-family: var(--main-Medium);
  padding: 15px;
  padding-left: 40px; 
  box-sizing: border-box; 
  resize: vertical; 
  border:#f2f2ff;

}

.input__right-logo {
  display: inline-flex;
  position: absolute;
  left: 16.34px;
  top: 17.5px;
  width: 15px;
  height: 15.68;
}
.input__left-logo {
  display: inline-flex;
  position: absolute;
  right: 16.35px;
  top: 17.5px;
}
.input::placeholder , .textAreaInput::placeholder {
  color: inherit;
  font-size: inherit;
  font-family: var(--main-Medium);
  color: #a7a9cb;
}
/* ---------.---------- */
.Auth-options {
  margin-top: 0px;
  margin-bottom: 5px;
  width: 100%;
}
.Auth-options__container {
  margin-top: 20px;
  display: flex;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}
.Auth-option__item-logo {
  height: 32.5px;
  margin: auto;
}
.Auth-option__item {
  flex: 1;
  cursor: pointer;
}
.Auth-option__item-title {
  margin-top: 10.5px;
  font-size: 14px;
  font-family: var(--main-Medium);
  color: var(--black);
}
.auth-container {
  margin-top: 60px;
  font-family: var(--main-Medium);
  margin-top: 50px;
}
.auth__link {
  color: var(--deep-purple);
  font-family: var(--main-Medium);
  display: inline;
  cursor: pointer;
}
.auth__link--disable {
  opacity: 0.8;
  cursor: initial;
}
.phone {
  font-family: var(--main-Medium);
  display: inline-block;
}
.input-date::-webkit-calendar-picker-indicator {
  background-image: url("../../keycloak-theme/images/icons8-birthday.svg");
  background-size: 16px 16px; /* Set the size of your custom icon */
  background-repeat: no-repeat;
  background-position: left;
  padding-right: 20px;
  position: absolute;
  left: 16.34px;
  top: 15px;
  fill: var(--main-color) !important;
}
.input-date {
  text-indent: 22px;
}
.input-row {
  display: flex;
  width: 100%;
  justify-content: space-between;
}
.input-row .Input-component-Wrapper:not(:first-child) {
  margin-left: 14px;
}
.input-row .radio-wrapper:not(:first-child) {
  margin-left: 14px;
}
.radio-wrapper {
  background-color: #f2f2ff;
  border-radius: 28px;
  color: var(--main-color);
  font-size: 14px;
  color: inherit;
  font-size: inherit;
  font-family: var(--main-Medium);
  display: flex;
  align-items: center;
  height: 50px;
}

.radio {
  display: none;
}

.radio-label {
  height: 100%;
  width: 100%;
  text-align: start;
  cursor: pointer;
  padding-left: 43px;
  line-height: 50px;
}

.radio-label:before {
  content: "";
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  border: 2px solid #25287f;
  border-radius: 50%;
  box-sizing: border-box;
}
.radio-label:after {
  content: "";
  display: block;
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: #25287f;
  opacity: 0;
}

.radio:checked + .radio-label:after {
  opacity: 1;
}

.checkbox-wrapper {
  align-items: center;
  margin-bottom: 10px;
  display: inline;
  font-family: var(--main-Medium);
}
.checkbox-wrapper:focus {
  outline: none;
}

.checkbox {
  display: none;
}
.decoration {
  text-decoration: underline;
}
.checkbox-label {
  display: inline-block;
  font-size: 12px;
  cursor: pointer;
  padding-left: 25px;
  position: relative;
}
.terms_condtion {
  font-size: 12px;
}
.checkbox-label:before {
  margin-left: 0px;
  content: "";
  position: absolute;
  left: 0;
  top: 8.5px;
  transform: translateY(-50%);
  width: 18px;
  height: 18px;
  border: 2px solid #aab9d5;
  border-radius: 5px;
  box-sizing: border-box;
}

.checkbox:checked + .checkbox-label:before {
  background-color: #25287f;
}
.checkbox-wrapper:focus > .checkbox-label:before,
.checkbox-wrapper:hover > .checkbox-label:before {
  background-color: #25287f;
  transform: scale(1.2);
  top: 0px;
}

.checkbox-wrapper:focus > .checkbox-label:after,
.checkbox-wrapper:hover > .checkbox-label:after {
  transform: scale(1.2);
  top: 8px;
  left: 25px;
}
.checkbox-label:after {
  content: "";
  display: block;
  position: absolute;
  top: 8px;
  margin-left: 0px;
  transform: translate(-50%, -50%);
  width: 12px;
  height: 6px;
  border: 3px solid #fff;
  border-top: none;
  border-right: none;
  margin-bottom: 13px;
  transform-origin: center;
  opacity: 0;
}

.checkbox:checked + .checkbox-label:after {
  margin-left: -16px;
  opacity: 1;
  transform: translate(-50%, -50%) rotate(-55deg);
}
.terms-condtions {
  margin-top: 20.3px;
  width: 100%;
  text-align: start;
}
.terms {
  color: var(--main-color);
  margin-top: 0px;
  font-family: var(--main-Medium);
  text-align: start;
  font-size: 16px;
}
.header-back__logo:hover,
.header-back__logo:focus {
  width: 50px;
  height: 50px;
  border: none;
  border-radius: 50%;

  background-color: rgb(37, 40, 127, 0.2);
  left: 10px;
  transition: 1;
}
.book {
  font-family: var(--main-Book);
}
.terms p:not(:first-child) {
  margin-top: 30px;
}
.medium {
  font-family: var(--main-Medium);
  color: 16px;
  color: var(--main-color);
}
.terms__Done {
  margin-top: 50px;
  background-color: var(--white);
  width: 100%;
  height: 75px;
  /* box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.75);
  -webkit-box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.75);
  -moz-box-shadow: 0px 0px 8px 0px rgba(0,0,0,0.75); */
  position: relative;
  bottom: 0px;
  left: 0px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.terms__button {
  height: 48px;
  width: 80%;
  font-family: var(--main-Medium);
}
.empty-div {
  height: 75px;
  background-color: none;
}
.Auth-operation-card {
  /* background-color: lightblue; */
  font-size: 16px;
  font-family: var(--main-Medium);
  height: calc(100% - 100px);
  top: 100px;
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.Auth-operation__button {
  color: var(--white);
  height: 48px;
  width: 100%;
  background: rgb(136, 37, 88);
  background: linear-gradient(
    170deg,
    rgba(136, 37, 88, 1) 46%,
    rgba(37, 40, 127, 1) 100%
  );
  border-radius: 28px;
  margin-top: 40px;
  font-size: 18px;
  cursor: pointer;
}
.Auth-operation__description {
  font-family: var(--main-Medium);
  font-size: 16px;
  margin-top: 10px;
}
.Auth-operation__action {
  margin-top: 40px;
  font-family: var(--main-Medium);
  font-size: 16px;
}
.Auth-operation__title {
  font-family: var(--main-Bold);
  font-size: 20px;
}
.Auth-operation__input {
  background-color: #aab9d5;
  margin-top: 20px;
  height: 60px;
  width: 100%;
}
.OTP-wrapper {
  width: 60px;
  height: 60px;
}
.otp-input {
  width: 100%;
  height: 100%;
  border-radius: 10px;
  font-size: 45px;
  text-indent: 18px;
  font-family: var(--main-Medium);
}
.otp-row {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.mg-50 {
  margin-left: 50px;
}
.otp-input::selection {
  color: var(--deep-purple);
}
.otp-input:focus {
  outline: none;
  border: 2px solid var(--main-color);
}

.radio-wrapper:focus {
  outline: none;
  border: 2px solid var(--main-color);
}
.input:focus , .textAreaInput:focus {
  outline: none;
  border: 2px solid var(--main-color);
}

.width100 {
  width: 100%;
  height: 100%;
  text-align: start;
  padding-top: 25px;
}
.selected_input {
  outline: none;
  border: 1px solid var(--main-color);
}
.custom-logo {
  fill: var(--main-color);
}
.input__right-logo--error {
  fill: var(--error-color);
}

.input--error {
  color: #b93535;
  background-color: var(--error-background);
  border: 1px solid #ab403b;
}

.error-message {
  color: #b93535;
  font-family: var(--main-Book);
  font-size: 14px;
  text-align: start;
  margin-top: 4.5px;
}

.input__label {
  display: block;
  color: var(--main-color);
  font-family: var(--main-Medium);
  font-size: 15px;
  margin-bottom: 5px;
  text-align: start;
  text-align: start;
}
.mg-bt-0 {
  margin-bottom: 0px;
}
.error {
  color: #b93535;
}
form {
  margin-top: 30px;
  width: 100%;
}
.error-login {
  text-align: center;
  margin-top: 20px;
}

.mg-tp-50 {
  margin-top: 5px;
}
.left-align {
  text-align: start;
}
.toast {
  color: var(--error-color);
  font-family: var(--main-Medium);
  box-shadow: 0px 3px 6px 7px #25287f14;
  border: 1px solid #25287f1c;
  min-height: 120px;
  width: 100%;
  height: fit-content;
  background-color: var(--white);
  border-radius: 10px;
  position: relative;
  animation: fade-in-from-left 1.5s 4s;
}
.toast-container {
  position: fixed;
  right: 25px;
  top: 30px;
  max-width: 472px;
  width: calc(100% - 50px);
  z-index: 4;
}
.hide {
  display: none;
}
.toast-container .toast:not(:first-child) {
  margin-top: 20px;
}
.toast__body {
  display: flex;
  align-items: start;
  justify-content: start;
  height: 112.5px;
  /* background-color: #571E3B; */
  justify-content: center;
  align-items: center;
  padding-left: 15px;
  border-radius: 10px;
}
.toast-bar {
  bottom: 0px;
  left: 0px;
  width: 0%;
  height: 7.5px;
  position: absolute;
  opacity: 1;
  animation: decrease-loade-time 5s backwards;
  border-radius: 0 0 0px 10px;
}
.success-bar {
  background-color: var(--toast-secuess);
}
.warning-bar {
  background-color: var(--toast-warning);
}
.failed-bar {
  background-color: var(--toast-danger);
}
.info-bar {
  background-color: var(--toast-info);
}
.success {
  color: var(--toast-secuess);
}
.warning {
  color: var(--toast-warning);
}
.failed {
  color: var(--toast-danger);
}
.info {
  color: var(--toast-info);
}
.toast-close {
  position: absolute;
  right: 10.5px;
  top: 5px;
  cursor: pointer;
  fill: #25287f;
}
.toast__text {
  font-family: var(--main-Book);
  font-size: 18px;
  margin-left: 12px;
  margin-right: auto;
}

.custom-kc-page-title {
  color: rgb(27, 35, 70);
  font-family: var(--main-Medium);
  font-size: 22px;
  margin-top: 20px;
  /* background-color: red; */
}
.custom-instruction {
  color: rgb(27, 35, 70);
  font-size: 18px;
  font-family: var(--main-Medium);
  margin-top: 25px;
}
.kc-backToApplication {
  color: gray;
  margin-top: 40px;
  display: block;
  font-size: 15px;
  margin-bottom: 0px;
}
.margin-top-5px {
  margin-top: 5px;
}
.page-expired {
  margin: 5px;
  font-size: 18px;
}
.page-expired:first-child {
  margin: 20px;
  font-size: 40px;
}

.card-padding {
  padding: 20px 20px;
  min-width: 400px;
}

@media (min-height: 1240px) {
  .app-lg {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: scroll;
  }
}
@media (max-height: 1239px) {
  .app-lg {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 100vh;
    overflow-y: scroll;
  }
  .card-container {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}

@media (min-height: 920px) {
  .app-md {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: scroll;
  }
}
@media (max-height: 919px) {
  .app-md {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 100vh;
    overflow-y: scroll;
  }
  .card-container {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}
@media (min-height: 765px) {
  .app-md-s {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: scroll;
  }
}
@media (max-height: 764px) {
  .app-md-s {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 100vh;
    overflow-y: scroll;
  }
  .card-container {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}
@media (min-height: 630px) {
  .app-sm {
    display: flex;
    justify-content: center;
    align-items: center;
    overflow-y: scroll;
  }
}
@media (max-height: 629px) {
  .app-sm {
    display: flex;
    justify-content: start;
    align-items: center;
    height: 100vh;
    overflow-y: scroll;
  }
  .card-container {
    margin-top: 60px;
    margin-bottom: 60px;
  }
}

@media (max-width: 472px) {
  .app {
    overflow: hidden;
  }
  .flex {
    height: 70%;
  }
  .keycloack-message__container {
    /* background-color: #913263; */
    height: calc(100vh - 200px);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .card-container {
    width: 100%;
    height: 100vh;
    margin-top: 0px;
    margin-bottom: 0px;
    border-radius: 0px;
    overflow: hidden;
    /* align-items: start; */
  }
  .card {
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  .front {
    align-items: start;
    width: 100%;
    height: 100%;
    margin-top: 0px;
    margin-bottom: 0px;
    border-radius: 0px;
    padding-bottom: 15px;
    padding-top: 15px;
    overflow-y: auto;
  }
  .card-container::after {
    content: "";
    position: fixed;
    top: -150.193359375px;
    right: -175px;
    width: 377px;
    height: 360px;
    background-size: auto;
    background-clip: content-box;
    background-image: url("../../keycloak-theme/images/Group_10249.svg");
    background-size: cover;
    opacity: 1;
    fill: red;
    pointer-events: none;
  }
  .card-container::before {
    content: "";
    position: fixed;
    bottom: -100px;
    right: -185px;
    width: 377px;
    height: 360px;
    background-size: auto;
    background-clip: content-box;
    background-image: url("../../keycloak-theme/images/Group_10249.svg");
    background-size: cover;
    opacity: 1;
    pointer-events: none;
    z-index: 2;
  }
  .app__container {
    /* background-color: #25287F; */
    padding: 0px 30px;
  }
}

.hide-icon {
  top: 13.5px;
}

a:focus {
  border: 2px solid var(--main-color);
}
.Auth-option__item-link:focus {
  display: block;
  width: 100%;
  height: 100%;
  transform: scale(1.02);
  transition: transform 0.5s ease;
  border: none;
}
.input:hover , .textAreaInput:hover {
  border: 0.2px solid #bfc0e7;
  transform: scale(1.01);
  transition: transform 0.5s ease;
}
.input:focus , .textAreaInput:focus{
  border: 2px solid #25287f;
}
.Auth-card__link:hover,
.Auth-card__link:focus {
  scale: 1.03;
  font-family: var(--main-Medium);
}

.Auth-option__item:hover {
  scale: 1.1;
}
.radio-wrapper:hover {
  border: 0.2px solid #bfc0e7;
  transform: scale(1.01);
  transition: transform 1s ease;
}
.auth__link:hover {
  font-family: var(--main-Bold);
  color: #571e3b;
}
.auth__link:focus {
  outline: none;
  font-family: var(--main-Bold);
  color: #571e3b;
}

.list-item {
  padding: 5px;
  cursor: pointer;
  text-align: left;
}

.list-item:hover {
  background-color: #f0f0f0;
}

.list {
  list-style: none;
  margin: 0px;
  padding: 5px;
}

.autocomplete {
  position: relative;
  display: inline-block;
  width: 100%;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 3s linear infinite;
}

.autocomplete-no-options,
.autocomplete-option {
  font-family: var(--main-Medium);
}
.primary{
  color:#25287f;
}
.info__title{
  margin-top: 50px;
}

.back-to-login__link {
  margin-top: 30px;
}

.otp-contact-info {
  font-family: var(--main-Medium);
  font-size: 16px;
  margin-top: 20px;
}