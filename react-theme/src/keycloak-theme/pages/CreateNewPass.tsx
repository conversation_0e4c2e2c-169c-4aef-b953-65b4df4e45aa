import React, { useEffect, useState, ChangeEvent, FormEvent } from "react";
import { InputPassword } from "../components/input/InputPassword";
import Icons8Eye from "../components/logo/Icons8Eye";
import Icons8Password from "../components/logo/Icons8Password";
import { useMutation } from "@apollo/client";
import { SET_PASSWORD } from "../GraphQL/Mutations";
import { usePageContext } from "../context/PageContext";
import { useToastContext } from "../Providers/ToastProvider";
import Icons8HiddenEye from "keycloak-theme/components/logo/Icon8HiddenEye";
import { useAppClass } from "keycloak-theme/context/AppClassContext";

interface CreateNewPassProps {
  appType?: string;
  passwordResetMethod?: string;
  username?: string;
  token?: string;
  vendor?: string;
}
interface FormData {
  password: string;
  confirmPassword: string;
}
interface FormErrors {
  firstName?: string;
  lastName?: string;
  email?: string;
  password?: string[];
  confirmPassword?: string;
  dateOfBirth?: string;
  gender?: string;
  termsAccepted?: string;
}

export default function CreateNewPass(props: CreateNewPassProps): JSX.Element {
  const { setAppClass } = useAppClass();
  const { pushToast } = useToastContext();
  const [formData, setFormData] = useState<FormData>({
    password: "",
    confirmPassword: "",
  });
  useEffect(() => {
    setAppClass("app-md");
  }, []);

  const { setPage, Routes } = usePageContext();

  const [errors, setErrors] = useState<FormErrors>({
    password: [],
    confirmPassword: "",
  });

  const [SetPassword] = useMutation(SET_PASSWORD);

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevFormData) => ({
      ...prevFormData,
      [name]: value,
    }));
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    const newErrors: FormErrors = {};

    newErrors.password = [];

    if (!formData.password) {
      newErrors.password?.push("Password is required");
    }

    if (formData.password.length < 8) {
      newErrors.password?.push("Password must be at least 8 characters long");
    }

    // Check for at least one uppercase letter
    if (!/[A-Z]/.test(formData.password)) {
      newErrors.password?.push(
        "Password must contain at least one uppercase letter"
      );
    }

    // Check for at least one special character
    if (!/[!@#$%^&*]/.test(formData.password)) {
      newErrors.password?.push(
        "Password must contain at least one special character (!@#$%^&*)"
      );
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = "Confirm Password is required";
    } else if (formData.confirmPassword.length < 8) {
      newErrors.confirmPassword = "Must be at least 8 characters ";
    } else if (formData.confirmPassword !== formData.password) {
      newErrors.confirmPassword =
        "Password and Confirm password should be same";
    } else {
      newErrors.confirmPassword = "";
    }
    setErrors({ ...errors, ...newErrors });

    if (Object.values(newErrors).some((error) => error.length > 0)) {
      return;
    }

    let response;
    try {
      response = await SetPassword({
        variables: {
          appType: props.appType,
          passwordResetMethod: props.passwordResetMethod,
          username: props.username,
          token: props.token,
          password: formData.password,
          vendor: props.vendor,
        },
      });
    } catch (error) {
      console.error("Error setting password:", error);
      pushToast("Something went wrong", "failed");
      return;
    }

    if (response.data.setPassword.success) {
      setPage(Routes.sign_in);
      pushToast("Password Changed Successfully", "success");
    } else {
      pushToast("Something went wrong", "failed");
    }
  };

  return (
    <div className="Auth-operation-card">
      <div className="Auth-operation__title">Create New Password</div>
      <div className="Auth-operation__description">
        Your new password must be different from previous passwords
      </div>
      <form onSubmit={handleSubmit}>
        <InputPassword
          inputProps={{
            autocomplete: "off",
            id: "pass",
            name: "password",
            placeholder: "Password",
            value: formData.password,
            onChange: handleChange,
          }}
          classes={{
            input_rightLogo_classes: errors.password?.length
              ? `input__right-logo--error`
              : "",
            input_leftLogo_classes: errors.password?.length
              ? `input__right-logo--error`
              : "custom-logo",
            input_classes: `${errors.password?.length ? "input--error" : ""}`,
          }}
          elements={{
            hideLogo: <Icons8HiddenEye width={"20.648px"} height="13.766px" />,
            showLogo: <Icons8Eye width="20.648px" height="13.766px" />,
            rightLogo: <Icons8Password width="15px" height="15.683px" />,
            inputMessage: errors?.password?.length ? (
              <div className="error-message">
                {errors.password.map((error) => (
                  <div>{`* ${error}.`}</div>
                ))}
              </div>
            ) : (
              ""
            ),
            inputLabel: (
              <label htmlFor={"pass"} className="input__label">
                {"Password"}
              </label>
            ),
          }}
        />
        <InputPassword
          inputProps={{
            autocomplete: "off",
            id: "confPass",
            name: "confirmPassword",
            placeholder: "Confirm Password",
            value: formData.confirmPassword,
            onChange: handleChange,
          }}
          classes={{
            input_rightLogo_classes: errors.confirmPassword
              ? `input__right-logo--error`
              : "",
            input_leftLogo_classes: errors.confirmPassword
              ? `input__right-logo--error`
              : "custom-logo",
            input_classes: `${errors.confirmPassword ? "input--error" : ""}`,
            input_component_Wrapper_classes: errors.confirmPassword
              ? "mg-bt-0"
              : "",
          }}
          elements={{
            hideLogo: <Icons8HiddenEye width={"20.648px"} height="13.766px" />,
            showLogo: <Icons8Eye width="20.648px" height="13.766px" />,
            rightLogo: <Icons8Password width="15px" height="15.683px" />,
            inputMessage: errors.confirmPassword ? (
              <div className="error-message">{errors.confirmPassword}</div>
            ) : (
              ""
            ),
            inputLabel: (
              <label htmlFor={"confPass"} className="input__label">
                {"Confirm Password"}
              </label>
            ),
          }}
        />
        <div className="">
          <button className="Auth-card__button">Reset Password</button>
        </div>
      </form>
    </div>
  );
}
