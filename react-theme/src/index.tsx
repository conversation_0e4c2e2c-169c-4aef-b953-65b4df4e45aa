import { createRoot } from "react-dom/client";
import { StrictMode, lazy, Suspense } from "react";
import { kcContext } from "./keycloak-theme/kcContext";
import { ThemeProvider } from "@mui/material";
import { CustomTheme } from "keycloak-theme/pages/shared/theme";
import "./reset.css"
import AppThemeProvider from "keycloak-theme/context/AppClassContext";
const App = lazy(() => import("./App"));
const KcApp = lazy(() => import("./keycloak-theme/KcApp"));

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <ThemeProvider theme={CustomTheme}>
      <Suspense>
        {kcContext === undefined ? <App /> : <AppThemeProvider><KcApp kcContext={kcContext} /></AppThemeProvider>}
      </Suspense>
    </ThemeProvider>
  </StrictMode>
);
