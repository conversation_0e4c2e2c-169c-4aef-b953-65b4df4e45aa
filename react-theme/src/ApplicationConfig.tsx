export type User = {
  type: string;
  client_id_in_URL: string;
  display_social_provider: boolean;
  display_signup: boolean;
  graphql_type: string;
  app_name: string;
  display_forget_password: boolean;
  card_app_type: string;
};
export const config = {
  RESEND_OTP_FOR_SIGNUP_IN_SECOUND: 30,
  RESEND_OTP_FOR_FORGETPASSWORD_IN_SECOUND: 30,
  APOLLO_SERVER:
    process.env.REACT_APP_API_URL || "https://api.sk-dev.sehacity.com/graphql",
  users: [
    {
      type: "admin",
      client_id_in_URL: "admin",
      display_social_provider: false,
      display_signup: false,
      graphql_type: "ADMIN",
      app_name: "ioHealth | Admin",
      display_forget_password: false,
      card_app_type: "app-md-s",
    },
    {
      type: "vendor",
      client_id_in_URL: "vendor",
      display_social_provider: false,
      display_signup: false,
      graphql_type: "VENDOR",
      app_name: "ioHealth | Vendor",
      display_forget_password: true,
      card_app_type: "app-md-s",
    },
    {
      type: "customer",
      client_id_in_URL: "customer",
      display_social_provider: true,
      display_signup: true,
      graphql_type: "CUSTOMER",
      app_name: "ioHealth | Customer",
      display_forget_password: true,
      card_app_type: "app-md",
    },
    {
      type: "any",
      client_id_in_URL: "",
      display_social_provider: false,
      display_signup: false,
      graphql_type: "",
      app_name: "ioHealth",
      display_forget_password: false,
      card_app_type: "app-md-s",
    },
  ],
};
