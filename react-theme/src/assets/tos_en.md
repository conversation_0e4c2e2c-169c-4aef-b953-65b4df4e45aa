# Terms of Service

## Presentation / Features

[EC: continuation of today's meeting: this would deserve to differentiate the SSP Cloud from the Onyxia SSP Cloud instance]

The SSP Cloud is a service (hereinafter referred to as "the service") implemented by the National Institute for Statistics and Economic Studies (hereinafter referred to as "Insee").

The SSP Cloud is an implementation of free software [Onyxia] (https://github.com/InseeFrLab/onyxia) created and maintained by the innovation and technical instruction division of INSEE (information system management / innovation unit and information system strategy). The SSP Cloud is hosted by INSEE.

[EC: I will remove the "on open data", since the SSP Cloud can accommodate secure data under the appropriate conditions]
The SSP Cloud is a platform offering a "datalab" intended for _data science_ experiments on open data in which users can orchestrate services dedicated to the practice of _data science_ (development environments, databases, etc.). This service offering thus aims to familiarize users with new collaborative working methods using _open source_ statistical languages ​​(R, python, Julia, etc.), _cloud computing_ type technologies, as well as to allow processing experiments. innovative statistics. The services offered are standard.

The SSP Cloud is aimed at officials of the official statistical system as well as teachers and students of the Group of National Schools of Economics and Statistics, allowing inter-service collaboration and cooperation with their ecosystem. Access can thus be granted on request and after decision of the governance bodies of the Cloud SSP to external collaborators and involved in the realization of experimental projects of the official statistical system. Projects involving non-open data are also subject to the decision of the governing bodies.

The SSP Cloud allows:

-   the orchestration of _data science_ trainings
-   access to _data science_ services
-   secure data storage
-   management of secrets, such as encryption keys
-   access to a code management service
-   orchestration of data processing flows

A user account is also used to connect to the service platform of the Inter-ministerial Mutualization Free Software community (<https://groupes.mim-libre.fr/>).

## Legal Notice

Functional administration of the Cloud SSP: Insee

This site is published by the National Institute for Statistics and Economic Studies (Insee).
INSEE
88 avenue Verdier
CS 70058
92541 Montrouge cedex

Director of publication: Mr. Jean-Luc Tavernier

Administrator: Frédéric Comte

Maintenance of the _open source_ Onyxia project: Insee

Hosting: Insee - Innovation and technical instruction division

## Terms of use of the Service

The SSP Cloud datalab can be accessed from any browser connected to
Internet. The use of a computer is recommended. Use of the datalab services is free.

The user community is accessible on:

-   Tchap, salon [SSP Cloud] (https://www.tchap.gouv.fr/#/room/#SSPCloudXDpAw6v:agent.finances.tchap.gouv.fr)
-   Rocket Chat at MIM Libre, [SSP Cloud] lounge (https://chat.mim-libre.fr/channel/sspcloud)

## Limits of use of the Service

Public data and data can be processed on the datalab
usual (working data without particular sensitivity). In the absence of specific authorization for a given experimental project, cannot be
"protected" or "sensitive" data processed on the datalab, with or without a
confidentiality intended to restrict distribution to a specific domain
(statistical, commercial, industrial secrecy, etc.).

[EC: seems too "weak" to me, refer to the opinion of the UAJC on this point: if an agent puts sensitive data on the datalab, under his responsibility, what is the responsibility of his employer? from INSEE? can be added "after he has taken a legal opinion on the character 'protected' or 'sensitive' and that he informed his hierarchy ??]
The "protected" or "sensitive" nature of the information stored or processed on the datalab
is subject to the discretion of the user under his own
responsibility.

## Roles, commitments and associated responsibilities

The service is made available by INSEE without other express guarantees or
tacit than those provided herein. The service is based on benchmark open source technologies. However, it is not guaranteed that it
is free from anomalies or errors. The service is therefore made available ** without
guaranteed availability and performance **. As such, INSEE cannot
be held responsible for loss and / or damage of any kind
be, who couldbe caused as a result of a malfunction or
unavailability of the service. Such situations will not give right to any
financial compensation.

Each user has a personal storage space. By default, all the information deposited in a user's storage space is accessible only to him. Each user has the possibility of making public files stored in their personal storage space. Each user is responsible for making their files available to the public.

[EC: take the opinion of the UAJC, I do not know if it is the user specifically who is responsible for the processing or the institution on which he depends]
Each user is responsible for processing all the experimental work he performs on the SSP Cloud.
He must, if necessary, declare the personal processing carried out using the SSP Cloud to the data protection officer of his structure and inform the members thereof. [not sure that it is only the DPD of his structure who must be aware, also the DPD Insee?]
[EC: in the case of a project involving several institutions, users must have previously established a data sharing / provision agreement.]

## Creating an account on the SSP Cloud

Access to the SSP Cloud requires prior registration and authentication.

## Experimental projects on sensitive data

** TODO **

Role of the project security manager

Enrollment of sensitive projects

Creation of collaborative spaces for sensitive projects

Creation and life cycle of spaces

## Processing of personal data

Data processing is based on the performance of the mission of providing a platform dedicated to experimentation and learning about data science for the benefit of the official statistical system.

The Service only collects the data strictly necessary for its implementation.
artwork.

The processing of personal data within the meaning of Articles 9 and 10 of
general data protection regulation (racial or ethnic origin,
political opinions, religious or philosophical beliefs, belonging
union, criminal convictions ...) is banned on the SSP Cloud.

[EC: same remark as above -> have the opinion of the Legal Unit]
Personal data processed as part of an experiment carried out by a user, when there is any, is the responsibility of the entity
administrative office from which the user originated. The
arrangements for their treatment must be communicated by
the user to the data protection officer of his entity
administrative unit.

Regarding the scope of the SSP Cloud service, the purpose of processing
concerns the management of the platform's accounts
(creation / conservation / deletion), operation of the platform (monitoring,
usage statistics) as well as the management of the services offered by the platform. Below is the list of
transverse personal data whose processing is under the
responsibility of INSEE.

** Suite to be managed with the DC POD **

> RL: @Fred, I put it a bit at random, I let you complete / amend

### Profile data

their first name, last name and email address (required);

freely:

-   photo (see gitlab)
-   ...

### Trace data

They are collected each time a user connects and, for example,
the use of a technical identifier, to trace connection operations and
modification of the objects of the service database.

They are used for technical support purposes. They can also do
subject to periodic review by the directors for control purposes and usage statistics.

### Cookie data

These cookies are only intended to allow the service to function and
to facilitate its use by users according to the constraints of each typology.

-   Session cookie: mandatory, it identifies the session of
    the user. The cookie is destroyed at the end of the session.

-   Reauthentication cookie: optional, it allows you to re-authenticate
    the user logged in for the duration of the cookie (one year maximum)

## Modification and evolution of the Service

INSEE reserves the right to develop, modify or suspend,
without notice, the Service for maintenance reasons or for any other
reason deemed necessary. The information is then communicated to users via Tchap.
The terms of these conditions of use may be modified or
completed at any time, without notice, depending on changes
made to the Service, changes in legislation or for any other reason
deemed necessary. These modifications and updates are binding on the user who
should therefore refer regularly to this section to verifythe
general conditions in force (accessible from the home page).

## Contact

For technical problems and / or
functionalities encountered on the platform, it is recommended, first of all
time to solicit communities of peers in collaborative spaces
provided for this purpose on Tchap and Rocket Chat-MIM Libre.

CNIL right of access for: <<EMAIL>>
