# Conditions générales d'utilisation

## Présentation / Fonctionnalités

[EC: suite de la réunion d'aujourdhui : cela mériterait de différencier le SSP Cloud de l'instance d'Onyxia SSP Cloud]

Le SSP Cloud est un service (ci après désigné par "le service") mis en œuvre par l'Institut national de la statistique et des études économiques (ci-après dénommé "l'Insee").

Le SSP Cloud est une implémentation du logiciel libre [Onyxia](https://github.com/InseeFrLab/onyxia) créé et maintenu par la division innovation et instruction technique de l'Insee (direction du système d'information/unité innovation et stratégie du système d'information). L’hébergement du SSP Cloud est assuré par l'Insee.

[EC: j'enlèverai le "sur données ouvertes", puisque le SSP Cloud peut accueillir dans les donditions idoines des données sécurisées]
Le SSP Cloud est une plateforme proposant un "datalab" destiné aux expérimentations de _data science_ sur données ouvertes dans lequel les utilisateurs peuvent orchestrer des services dédiés à la pratique de la _data science_ (environnements de développement, bases de données...). Cette offre de services vise ainsi à familiariser les utilisateurs avec de nouvelles méthodes de travail collaboratif mobilisant des langages statistiques _open source_ (R, python, Julia...), des technologies de type _cloud computing_ ainsi qu'à permettre d'expérimenter des traitements statistiques innovants. Les services proposés sont standards.

Le SSP Cloud s’adresse aux agents du système statistique public ainsi qu'aux enseignants et étudiants du Groupe des écoles nationales d'économie et de statistique, permettant une collaboration interservices et la coopération avec leur écosystème. Des accès peuvent ainsi être accordés sur demande et après décision des organes de gouvernance du SSP Cloud à des collaborateurs extérieurs et impliqués dans la réalisation de projets expérimentaux du système statistique public. Les projets mobilisant des données non ouvertes sont aussi soumis à la décision des organes de gouvernance.

Le SSP Cloud permet :

-   l'orchestration de formations de _data science_
-   l'accès à des services de _data science_
-   le stockage sécurisé de données
-   la gestion de secrets, tels que des clés de chiffrement
-   l'accès à un service de gestion de code
-   l'orchestration de flux de traitement de données

Un compte utilisateur permet également de se connecter à la plateforme de services de la communauté Mutualisation Inter-ministérielle Logiciels Libres (<https://groupes.mim-libre.fr/>).

## Mentions légales

Administration fonctionnelle du SSP Cloud : Insee

Ce site est édité par l'Institut national de la statistique et des études économiques (Insee).
Insee
88 avenue Verdier
CS 70058
92541 Montrouge cedex

Directeur de la publication : Monsieur Jean-Luc Tavernier

Administrateur : Frédéric Comte

Maintenance du projet _open source_ Onyxia : Insee

Hébergement : Insee - Division innovation et instruction technique

## Modalités d’utilisation du Service

Le datalab SSP Cloud est accessible depuis n’importe quel navigateur connecté à
Internet. L'utilisation d'un ordinateur est recommandée. L’utilisation des services du datalab est gratuite.

La communauté d'utilisateurs est accessible sur :

-   Tchap, salon [SSP Cloud](https://www.tchap.gouv.fr/#/room/#SSPCloudXDpAw6v:agent.finances.tchap.gouv.fr)
-   Rocket Chat du MIM Libre, salon [SSP Cloud](https://chat.mim-libre.fr/channel/sspcloud)

## Limites d’utilisation du Service

Peuvent être traitées sur le datalab les données publiques et données
usuelles (données de travail sans sensibilité particulière). En l'absence d'autorisation spécifique pour un projet d'expérimentation donné, ne peuvent être
traitées sur le datalab les données ‘protégées’ ou ‘sensibles’, avec ou sans marque de
confidentialité destinée à restreindre la diffusion à un domaine spécifique
(secret statistique, commercial, industriel..).

[EC: me semble trop "faible", se référer à l'avis de l'UAJC sur ce point : si un agent met des données sensibles sur le datalab, sous sa responsabilité, quelle est la responsabilité de son employeur? de l'Insee ? peut être ajouter "après qu'il ait pris un avis juridique sur le caractère 'protégé' ou 'sensible' et qu'il en ait informé sa hiérarchie??]
Le caractère ‘protégé’ ou ‘sensible’ des informations stockées ou traitées sur le datalab
est soumis à l’appréciation de l’utilisateur sous sa propre
responsabilité.

## Les rôles, engagements et responsabilités associées

Le service est mis à disposition par l'Insee sans autres garanties expresses ou
tacites que celles qui sont prévues par les présentes. Le service s’appuie sur des technologies open source de référence. Toutefois, il n’est pas garanti qu’il
soit exempt d’anomalies ou erreurs. Le service est donc mis à disposition **sans
garantie sur sa disponibilité et ses performances**. A ce titre, l'Insee ne peut
être tenue responsable des pertes et/ou préjudices, de quelque nature qu’ils
soient, qui pourraient être causés à la suite d’un dysfonctionnement ou une
indisponibilité du service. De telles situations n'ouvriront droit à aucune
compensation financière.

Chaque utilisateur dispose d'un espace de stockage personnel. Par défaut, toutes les informations déposées dans un espace de stockage d'un utilisateur ne sont accessibles qu'à lui seul. Chaque utilisateur a la possibilité de rendre publics des fichiers stockés dans son espace de stockage personnel. Chaque utilisateur est responsable de la mise à disposition publique de ses fichiers.

[EC : prendre l'avis de l'UAJC, je ne sais pas si c'est l'utilisateur nommément qui est responsable du traitement ou bien l'institution dont il dépend]
Chaque utilisateur est responsable de traitement pour l’ensemble des travaux d'expérimentation qu'il réalise sur le SSP Cloud.
Il doit, le cas échant, déclarer les traitements à caractère personnel réalisés à l'aide du SSP Cloud au délégué à la protection des données de sa structure et en informer les membres. [pas sur que ce soit uniquement le DPD de sa structure qui doit être au courant, aussi le DPD Insee?]
[EC : dans le cas d'un projet faisant intervenir plusieurs institutions, les utilisateurs doivent avoir au préalable établi un conventionnement de partage/ mise à disposition des données.]

## La création de compte sur le SSP Cloud

L'accès au SSP Cloud nécessite une inscription préalable et une authentification.

## Les projets d'expérimentation sur données sensibles

**TODO**

Rôle du responsable de sécurité du projet

Enrôlement des projets sensibles

Création d'espaces collaboratifs pour les projets sensibles

Création et cycle de vie des espaces

## Traitement des données à caractère personnel

Le traitement des données se fonde sur l’exécution de la mission que constitue la mise à disposition d'une plateforme dédiée à l'expérimentation et à l'apprentissage de la datascience au bénéfice du système statistique public.

Le Service ne collecte que les données strictement nécessaires à sa mise en
œuvre.

Le traitement de données à caractère personnel au sens des articles 9 et 10 du
règlement général sur la protection des données (origine raciale ou ethnique,
opinions politiques, convictions religieuses ou philosophiques, appartenance
syndicale, condamnations pénales...) est proscrit sur le SSP Cloud.

[EC: meme remarque que ci-dessus --> avoir l'avis de l'Unité juridique]
Les données à caractère personnel traitées dans le cadre d'une expérimentation réalisée par un utilisateur, quand il y en a, relèvent de la responsabilité de l’entité
administrative dont est issu l’utilisateur. Les
dispositions relatives à leur traitement doivent être communiquées par
l'utilisateur au délégué à la protection des données de son entité
administrative de rattachement.

Pour ce qui est du périmètre du service SSP Cloud, la finalité de traitement
concerne la gestion des comptes de la plateforme
(création/conservation/suppression), l’exploitation de la plateforme (suivi,
statistiques d’usages) ainsi que la gestion des services offerts par la plateforme. Ci-dessous la liste des
données à caractère personnel transverses dont le traitement est sous la
responsabilité de l'Insee.

**Suite à gérer avec le DC POD**

> RL : @Fred, je mets un peu au hasard, je te laisse compléter/amender

### Données relatives au profil

ses prénom, nom et adresse mail (obligatoire) ;

de façon libre :

-   photo (cf. gitlab)
-   ...

### Données de trace

Elles sont collectées à chaque connexion d'un utilisateur et permettent, par
l’utilisation d’un identifiant technique, de tracer les opérations de connexion et
de modification des objets de la base de données du service.

Elles servent à des fins de support technique. Elles peuvent également faire
l'objet d'une revue périodique de la part des administrateurs à des fins de contrôle et de statistiques d'usage.

### Les données de cookies

Ces cookies n’ont pour objet que de permettre le fonctionnement du service et
de faciliter son usage par les utilisateurs selon les contraintes chaque typologie.

-   Cookie de session : obligatoire , il permet d'identifier la session de
    l'utilisateur. Le cookie est détruit à la fin de la session.

-   Cookie de réauthentification : optionnel, il permet de ré-authentifier
    l'utilisateur connecté pendant la durée du cookie (un an maximum)

## Modification et évolution du Service

L'Insee se réserve la liberté de faire évoluer, de modifier ou de suspendre,
sans préavis, le Service pour des raisons de maintenance ou pour tout autre
motif jugé nécessaire. L'information est alors communiquée aux utilisateurs via Tchap.
Les termes des présentes conditions d’utilisation peuvent être modifiés ou
complétés à tout moment, sans préavis, en fonction des modifications
apportées au Service, de l’évolution de la législation ou pour tout autre motif
jugé nécessaire. Ces modifications et mises à jour s’imposent à l’utilisateur qui
doit, en conséquence, se référer régulièrement à cette rubrique pour vérifier les
conditions générales en vigueur (accessible depuis la page d’accueil).

## Contact

Pour les problèmes techniques et/ou
fonctionnels rencontrés sur la plateforme, il est conseillé, dans un premier
temps de solliciter les communautés de pairs dans les espaces collaboratifs
prévus à cet effet sur Tchap et Rocket Chat-MIM Libre.

Droit d’accès CNIL pour : <<EMAIL>>
