/* font */
@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@600&display=swap");

/* src/fonts.css */
@font-face {
  font-family: "book";
  font-weight: normal;
  font-style: normal;
  src: url("./fonts/airbnb_cereal_app/airbnb-cereal-app-book.ttf")
    format("truetype");
}

@font-face {
  font-family: "bold";
  font-weight: normal;
  font-style: normal;
  src: url("./fonts/airbnb_cereal_app/airbnb-cereal-app-bold.ttf")
    format("truetype");
}

@font-face {
  font-family: "medium";
  font-weight: normal;
  font-style: normal;
  src: url("./fonts/airbnb_cereal_app/airbnb-cereal-app-medium.ttf")
    format("truetype");
}

@font-face {
  font-family: "black";
  font-weight: normal;
  font-style: normal;
  src: url("./fonts/airbnb_cereal_app/airbnb-cereal-app-black.ttf")
    format("truetype");
}
/* RESET DEFAULT STYLE*/
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
:root {
  --white: #fff;
  --nav_bar_width: 55px;
  --main-color: #25287f;
  --black: #000;
  --deep-purple: #913263;
  --background-1: #f8f9fb;
  --error-color: #b93535;
  --error-background: #fff7f7;
  --main-Book: "book";
  --main-Bold: "bold";
  --main-Medium: "medium";
  --main-Black: "black";
  --toast-warning: #d46a3d;
  --toast-danger: #b93535;
  --toast-secuess: #46a200;
  --toast-info: #252880;
}
*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
body {
  overflow: hidden;
}
input {
  font: inherit;
  border: none;
}

button {
  border: none;
  background-color: initial;
  outline: none;
}

ul {
  list-style: none;
}

a {
  color: inherit;
  text-decoration: none;
  outline: none;
}

.app {
  font-size: 15px;
  color: #fff;
  background-color: var(--white);
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: rgb(37, 40, 127);
  background: linear-gradient(
    105deg,
    rgba(37, 40, 127, 1) 0%,
    rgba(40, 40, 126, 1) 21%,
    rgba(53, 40, 121, 1) 46%,
    rgba(117, 38, 95, 1) 75%,
    rgba(136, 37, 88, 1) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  color: var(--main-color);
  text-align: center;
  font-size: 18px;
  font-family: var(--main-Book);
  font-weight: normal;
}
.app::after {
  content: "";
  position: fixed;
  top: -123.193359375px;
  left: -190.8359375px;
  width: 613px;
  height: 622px;
  background-size: auto;
  background-clip: content-box;
  background-image: url("./assets/logos/Group 10248.svg");
  background-size: cover;
  /* opacity: 1;  */
  pointer-events: none;
  z-index: 0;
}
.app::before {
  content: "";
  position: fixed;
  bottom: -150px;
  right: -150px;
  width: 505px;
  height: 505px;
  background-size: auto;
  background-clip: content-box;
  background-image: url("./assets/logos/Group 10249.svg");
  background-size: cover;
  z-index: 0;
  pointer-events: none;
}
/* ... (previous CSS code) */

.card-container {
  max-width: 472px;
  height: fit-content;
  width: 95%;
  z-index: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.card {
  width: 100%;
  height: 100%;
}

.custom-rotate {
  animation-name: rotate-animation;
  animation-duration: 1s;
  animation-fill-mode: forwards;
  transform-style: preserve-3d;
}

@keyframes rotate-animation {
  0% {
    transform: rotateY(180deg);
  }
  100% {
    transform: rotateY(0deg);
  }
}
.card-side {
  width: 100%;
  background-color: #fff;
  box-shadow: 0px 3px 6px #0000000d;
  border-radius: 15px;
  padding-bottom: 30px;
  padding-top: 30px;
  z-index: 2;
  opacity: 1;
}
.front {
  backface-visibility: hidden;
  transform: rotateY(0deg);
}
.back {
  backface-visibility: hidden;
  position: absolute;
  top: 0;
  left: 0;
  transform: rotateY(180deg);
}
.display-none {
  display: none;
  visibility: hidden;
  z-index: -70;
}
