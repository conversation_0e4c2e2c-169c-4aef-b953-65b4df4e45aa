name: ci
on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:

  build:
    runs-on: windows-latest
    if: github.event.head_commit.author.name != 'actions'
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v2.1.3
      with:
        node-version: '16'
    - uses: bahmutov/npm-install@v1
    - run: yarn build
    - run: npx keycloakify
    - uses: actions/upload-artifact@v2
      with:
        name: standalone_keycloak_theme
        path: build_keycloak/target/*keycloak-theme*.jar
    - run: npx keycloakify --external-assets
    - uses: actions/upload-artifact@v2
      with:
        name: keycloak_theme
        path: build_keycloak/target/*keycloak-theme*.jar
    - uses: actions/upload-artifact@v2
      with:
        name: build
        path: build

  check_if_version_upgraded:
    name: Check if version upgrade
    runs-on: ubuntu-latest
    needs: build
    outputs:
      from_version: ${{ steps.step1.outputs.from_version }}
      to_version: ${{ steps.step1.outputs.to_version }}
      is_upgraded_version: ${{ steps.step1.outputs.is_upgraded_version }}
    steps:
    - uses: garronej/ts-ci@v1.1.7
      id: step1
      with: 
        action_name: is_package_json_version_upgraded

  github_pages:
    runs-on: ubuntu-latest
    needs: 
      - check_if_version_upgraded
      - build
    # We publish the the docker image only if it's a push on the default branch or if it's a PR from a 
    # branch (meaning not a PR from a fork). It would be more straightforward to test if secrets.DOCKERHUB_TOKEN is 
    # defined but GitHub Action don't allow it.
    if: |
      needs.check_if_version_upgraded.outputs.is_upgraded_version == 'true' &&
      github.event_name == 'push' || 
      github.event.pull_request.head.repo.owner.login == github.event.pull_request.base.repo.owner.login 
    steps:
    - uses: actions/checkout@v2
    - uses: actions/download-artifact@v2
      with:
        name: build
        path: build
    - uses: actions/setup-node@v2.1.3
      with:
        node-version: '15'
    - run: echo $(node -e 'console.log(require("url").parse(require("./package.json").homepage).host)') > build/CNAME
    - run: git remote set-url origin https://git:${GITHUB_TOKEN}@github.com/${{github.repository}}.git
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
    - run: npx -y -p gh-pages@3.1.0 gh-pages -d build -u "github-actions-bot <<EMAIL>>"

  docker:
    needs: 
      - check_if_version_upgraded
      - build
    runs-on: ubuntu-latest
    # We publish the the docker image only if it's a push on the default branch or if it's a PR from a 
    # branch (meaning not a PR from a fork). It would be more straightforward to test if secrets.DOCKERHUB_TOKEN is 
    # defined but GitHub Action don't allow it.
    if: |
      github.event_name == 'push' || 
      github.event.pull_request.head.repo.owner.login == github.event.pull_request.base.repo.owner.login 
    steps:
      - uses: actions/checkout@v2
      - uses: docker/setup-qemu-action@v1
      - uses: docker/setup-buildx-action@v1
      - uses: docker/login-action@v1
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: Computing Docker image tags
        id: step1
        env: 
          IS_UPGRADED_VERSION: ${{ needs.check_if_version_upgraded.outputs.is_upgraded_version }}
          TO_VERSION: ${{ needs.check_if_version_upgraded.outputs.to_version }}
        run: |
          OUT=$GITHUB_REPOSITORY:$(
          [ "$GITHUB_EVENT_NAME" == "pull_request" ] &&
            echo "$GITHUB_HEAD_REF" ||
            echo "${GITHUB_REF#refs/*/}"
          )
          if [ "$IS_UPGRADED_VERSION" = "true" ]; then
            OUT=$OUT,$GITHUB_REPOSITORY:$TO_VERSION,$GITHUB_REPOSITORY:latest
          fi
          OUT=$(echo "$OUT" | awk '{print tolower($0)}') 
          echo ::set-output name=docker_tags::$OUT
      - uses: actions/download-artifact@v2
        with:
          name: build
          path: build
      - run: tar -cvf build.tar ./build
      - uses: docker/build-push-action@v2
        with:
          push: true
          context: .
          tags: ${{ steps.step1.outputs.docker_tags }}
          file: Dockerfile.ci

  create_github_release:
    runs-on: ubuntu-latest
    needs: 
      - check_if_version_upgraded
    # We create a release only if the version have been upgraded and we are on a default branch
    # PR on the default branch can release beta but not real release
    if: |
      needs.check_if_version_upgraded.outputs.is_upgraded_version == 'true' &&
      (
        github.event_name == 'push' ||
        needs.check_if_version_upgraded.outputs.is_release_beta == 'true'
      )
    steps:
    - uses: actions/download-artifact@v2
      with:
        name: keycloak_theme
    - run: mkdir jars
    - run: mv *keycloak-theme*.jar jars/keycloak-theme.jar
    - uses: actions/download-artifact@v2
      with:
        name: standalone_keycloak_theme
    - run: mv *keycloak-theme*.jar jars/standalone-keycloak-theme.jar
    - uses: softprops/action-gh-release@v1
      with:
        name: Release v${{ needs.check_if_version_upgraded.outputs.to_version }}
        tag_name: v${{ needs.check_if_version_upgraded.outputs.to_version }}
        target_commitish: ${{ github.head_ref || github.ref }}
        generate_release_notes: true
        files: |
          jars/keycloak-theme.jar
          jars/standalone-keycloak-theme.jar
        draft: false
        prerelease: ${{ needs.check_if_version_upgraded.outputs.is_release_beta == 'true' }}
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        
