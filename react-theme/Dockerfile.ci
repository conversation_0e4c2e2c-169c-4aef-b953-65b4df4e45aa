# This is an alternative Dockerfile 
# that aims to be used in the CI pipeline.
# In this version we assume that the app have been build (yarn build that generate ./build/)
# prior and archived into a build.tar file present in the context.
# We do do that because 
# 1) We want to avoid building the app twice, one for the docker image and one for the theme .tar
# 2) If we use keycloakify --external-assets we have to generate the theme from the build/ directory
# that is going to be in production. (CRA generates hashes, every build is different, even if the code is the same)

# build environment
FROM alpine as build
WORKDIR /app
#We use ADD instead of COPY because build/ is in .dockerignore
ADD build.tar .
COPY nginx.conf .

# production environment (copy pasted from ./Dockerfile)
FROM nginx:stable-alpine
COPY --from=build /app/build /usr/share/nginx/html
COPY --from=build /app/nginx.conf /etc/nginx/conf.d/default.conf
CMD nginx -g 'daemon off;'