package com.i3hub.uae.pass.resource;


import com.fasterxml.jackson.core.JsonProcessingException;
import com.i3hub.uae.pass.client.GraphQLClient;
import com.i3hub.uae.pass.model.BiometricLoginRequest;
import com.i3hub.uae.pass.model.*;
import com.i3hub.uae.pass.mutations.RequestOTPMutation;
import com.i3hub.uae.pass.util.Base64Util;
import com.i3hub.uae.pass.util.DateUtil;
import com.i3hub.uae.pass.util.TokenUtil;
import com.i3hub.uae.pass.util.UserUtil;
import org.keycloak.events.Errors;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.UserModel;
import org.keycloak.models.UserSessionModel;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.services.managers.AppAuthManager;
import org.keycloak.services.managers.AuthenticationManager.AuthResult;
import org.keycloak.services.resource.RealmResourceProvider;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.Response;
import java.util.*;

/**
 * <AUTHOR> Köbler, https://www.n-k.de, @dasniko
 */

public class CustomAPIProvider implements RealmResourceProvider {

    private final KeycloakSession session;


    public CustomAPIProvider(KeycloakSession session) {
        this.session = session;

    }

    @Override
    public Object getResource() {
        return this;
    }

    @Override
    public void close() {
    }

    @POST
    @Path("impersonate")
    @Produces(MediaType.APPLICATION_JSON)
    public Response impersonate(@QueryParam("userId") String userId) {
        AuthResult authResult = checkAuth();
        UserModel currentUser = authResult.getUser();
        UserSessionModel currentUserSession = authResult.getSession();
        Long id = UserUtil.decodeUserID(userId);
        UserModel user = UserUtil.getUserByUserId(session, session.getContext(), String.valueOf(id));
        if (user != null) {
            if (UserUtil.validateParentChildRelation(Arrays.asList(currentUser, user)) || UserUtil.validateChildsRelation(currentUser, user)) {
                return handleCores().entity(TokenUtil.impersonate(session, currentUserSession, user, true)).status(Response.Status.OK).build();
            } else {
                throw new NotAuthorizedException("Bearer");
            }
        } else {
            throw new NotAuthorizedException("Bearer");
        }
    }

    @POST
    @Path("impersonate-customer-user")
    @Produces(MediaType.APPLICATION_JSON)
    public Response impersonateCustomerUser(@QueryParam("userName") String userName) {
        AuthResult authResult = checkAuth();
        UserModel currentUser = authResult.getUser();
        if (currentUser.getRoleMappingsStream().noneMatch(role -> role.getName().equals("impersonate_user"))) {
            throw new NotAuthorizedException("Bearer");
        }
        UserSessionModel currentUserSession = authResult.getSession();
        LoginRequest loginRequest = new LoginRequest();
        loginRequest.setUsername(userName);
        loginRequest.setAppType("Customer");
        UserModel user = UserUtil.getUser(session, loginRequest);
        if (user == null) {
            throw new NotAuthorizedException("Bearer");
        }
        if (user.getId().equals(currentUser.getId())) {
            throw new BadRequestException("User cannot impersonate itself");
        }
        if (user.isEnabled()) {
            return handleCores().entity(TokenUtil.impersonate(session, currentUserSession, user, false)).status(Response.Status.OK).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }
    }


    @POST
    @Path("impersonate-user")
    @Produces(MediaType.APPLICATION_JSON)
    public Response impersonateUser(@QueryParam("userId") String userId) {
        AuthResult authResult = checkAuth();
        UserModel currentUser = authResult.getUser();
        if (currentUser.getRoleMappingsStream().noneMatch(role -> role.getName().equals("impersonate_user"))) {
            throw new NotAuthorizedException("Bearer");
        }

        UserSessionModel currentUserSession = authResult.getSession();

        UserModel user = UserUtil.getUserById(session, userId);
        if (user == null) {
            throw new NotAuthorizedException("Bearer");
        }
        if (user.getId().equals(currentUser.getId())) {
            throw new BadRequestException("User cannot impersonate itself");
        }
        if (user.isEnabled()) {
            return handleCores().entity(TokenUtil.impersonate(session, currentUserSession, user, false)).status(Response.Status.OK).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }
    }

    @POST
    @Path("impersonate-provider-user")
    @Produces(MediaType.APPLICATION_JSON)
    public Response impersonateProviderUser(@QueryParam("email") String email) {
        AuthResult authResult = checkAuth();
        UserModel currentUser = authResult.getUser();
        //print user roles

        if (currentUser.getRoleMappingsStream().noneMatch(role -> role.getName().equals("impersonate_provider_user"))) {
            System.out.println("User does not have role to impersonate provider user");
            throw new NotAuthorizedException("Bearer");
        }

        UserSessionModel currentUserSession = authResult.getSession();
        UserModel user = UserUtil.getUserByEmail(session, session.getContext(), email);
        if (user == null || currentUser.getAttributes().getOrDefault("vendor_id", null) == null || user.getAttributes().getOrDefault("vendor_id", null) == null) {
            throw new NotAuthorizedException("Bearer");
        }
        if (user.getId().equals(currentUser.getId())) {
            throw new BadRequestException("User cannot impersonate itself");
        }
        Long currentUserVendorId = Long.parseLong(currentUser.getAttributes().get("vendor_id").get(0));
        Long userVendorId = Long.parseLong(user.getAttributes().get("vendor_id").get(0));
        if (user != null && user.isEnabled() && currentUserVendorId.equals(userVendorId)) {
            return handleCores().entity(TokenUtil.impersonate(session, currentUserSession, user, false)).status(Response.Status.OK).build();
        } else {
            System.out.println("vendor id not matched");
            throw new NotAuthorizedException("Bearer");
        }
    }

    private TwoFAResponse handleTFA(GraphQLClient graphQLClient,UserModel user){
        TwoFAResponse twoFAResponse = new TwoFAResponse();
        twoFAResponse.setUserId(user.getId());
        List<String> twoFaEnabled = user.getAttributes().getOrDefault("two_factor_auth_enabled", new ArrayList<>());
        if (!twoFaEnabled.isEmpty() && Boolean.parseBoolean(twoFaEnabled.get(0))) {
            String twoFaMethod = user.getFirstAttribute("two_factor_auth_verification_method").toUpperCase();
            twoFAResponse.setVerificationMethod(twoFaMethod.toUpperCase());
            RequestOTPMutation.RequestOtp requestOtp = null;
            switch (twoFaMethod) {
                case "EMAIL":
                case "MOBILE":
                    requestOtp = graphQLClient.requestOtp(twoFaMethod, user.getFirstAttribute("mobile"), user.getEmail());
                    twoFAResponse.setSessionToken(requestOtp.getSessionToken().get());
                    break;
                case "AUTHENTICATOR":
                    twoFAResponse.setSessionToken(Base64Util.encode(UUID.randomUUID().toString()));
                    break;
            }
            if (requestOtp != null) {
                twoFAResponse.setSessionToken(requestOtp.getSessionToken().get());
            }
            return twoFAResponse;
        }else{
            return null;
        }
    }

    @POST
    @Path("logout")
    @Produces(MediaType.APPLICATION_JSON)
    public Response logout() {
        AuthResult authResult = checkAuth();
        UserSessionModel userSession = authResult.getSession();
        if (userSession != null) {
            session.sessions().removeUserSession(session.getContext().getRealm(), userSession);
            return Response.ok().entity(Collections.singletonMap("message", "Logged out successfully")).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }
    }

    @POST
    @Path("login")
    @Produces(MediaType.APPLICATION_JSON)
    public Response login(LoginRequest loginRequest) {
        GraphQLClient graphQLClient = new GraphQLClient();

        try {
            UserModel user = UserUtil.getUser(session, loginRequest);

            if (user == null) {
                throw new NotAuthorizedException("Bearer");
            }

            if (!user.isEnabled()) {
                throw new NotAuthorizedException("Bearer");
            }

            if (isTemporarilyDisabledByBruteForce(user)) {
                throw new NotAuthorizedException("Bearer");
            }


            if (!UserUtil.validateCredentials(user, loginRequest.getPassword())) {
                if (session.getContext().getRealm().isBruteForceProtected()) {
                    session.getProvider(org.keycloak.services.managers.BruteForceProtector.class)
                           .failedLogin(session.getContext().getRealm(), user,
                                      session.getContext().getConnection());
                }
                throw new NotAuthorizedException("Bearer");
            }

            // Check age restriction
            List<String> dateOfBirthAttribute = user.getAttributes().getOrDefault("date_of_birth", new ArrayList<>());
            if (!dateOfBirthAttribute.isEmpty()) {
                Date dateOfBirth = DateUtil.parseDate(dateOfBirthAttribute.get(0));
                if (DateUtil.calculateAge(dateOfBirth) < 18) {
                    throw new NotAuthorizedException("Bearer");
                }
            }

            if (session.getContext().getRealm().isBruteForceProtected()) {
                session.getProvider(org.keycloak.services.managers.BruteForceProtector.class)
                       .successfulLogin(session.getContext().getRealm(), user,
                                      session.getContext().getConnection());
            }

            // Check if 2FA is enabled
            TwoFAResponse twoFAResponse = handleTFA(graphQLClient, user);
            if(twoFAResponse != null){
                return Response.ok().entity(twoFAResponse).build();
            }

            AccessTokenResponse accessToken = TokenUtil.getAccessToken(user, session, false);
            return handleCores().entity(accessToken).status(Response.Status.OK).build();

        } catch (Exception e) {
            throw e;
        }
    }

    private boolean isTemporarilyDisabledByBruteForce(UserModel user) {
        if (session.getContext().getRealm().isBruteForceProtected() && user != null) {
            return session.getProvider(org.keycloak.services.managers.BruteForceProtector.class)
                    .isTemporarilyDisabled(session, session.getContext().getRealm(), user);
        }
        return false;
    }

    @POST
    @Path("verify-code")
    @Produces(MediaType.APPLICATION_JSON)
    public Response verifyCode(VerifyCodeRequest verifyCodeRequest) {
        UserModel user = UserUtil.getUserById(session, verifyCodeRequest.getUserId());
        if (user == null) {
            throw new NotAuthorizedException("Bearer");
        }
        List<String> twoFaEnabled = user.getAttributes().getOrDefault("two_factor_auth_enabled", new ArrayList<>());
        if (!twoFaEnabled.isEmpty() && Boolean.parseBoolean(twoFaEnabled.get(0)) && verifyCode(user, verifyCodeRequest)) {
            AccessTokenResponse accessToken = TokenUtil.getAccessToken(user, session, false);
            return handleCores().entity(accessToken).status(Response.Status.OK).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }

    }

    @POST
    @Path("biometric-login")
    @Produces(MediaType.APPLICATION_JSON)
    public Response biometricLogin(BiometricLoginRequest verifyCodeRequest) throws JsonProcessingException {
        GraphQLClient graphQLClient = new GraphQLClient();
        Long userId = graphQLClient.biometricLogin(verifyCodeRequest);
        if (userId != null) {
            UserModel user = UserUtil.getUserByUserId(session, session.getContext(), String.valueOf(userId));
            if (user == null) {
                throw new NotAuthorizedException("Bearer");
            }

            // Check if 2FA is enabled
            TwoFAResponse twoFAResponse = handleTFA(graphQLClient,user);
            if(twoFAResponse != null){
                return Response.ok().entity(twoFAResponse).build();
            }

            AccessTokenResponse accessToken = TokenUtil.getAccessToken(user, session, false);
            return handleCores().entity(accessToken).status(Response.Status.OK).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }

    }


    @POST
    @Path("otp-login")
    @Produces(MediaType.APPLICATION_JSON)
    public Response loginOtp(OtpLoginRequest otpLoginRequest) {
        GraphQLClient graphQLClient = new GraphQLClient();
        Boolean result = graphQLClient.verifyOtpForLogin(otpLoginRequest);
        if (result == true) {
            UserModel user = null;
            if (otpLoginRequest.getEmail() != null) {
                user = UserUtil.getUserByEmail(session, session.getContext(), otpLoginRequest.getEmail());
            }
            if (otpLoginRequest.getMobile() != null) {
                user = UserUtil.getUserByMobile(session, session.getContext(), otpLoginRequest.getMobile());
            }
            if (user == null) {
                throw new NotAuthorizedException("Bearer");
            }
            AccessTokenResponse accessToken = TokenUtil.getAccessToken(user, session, false);
            return handleCores().entity(accessToken).status(Response.Status.OK).build();
        } else {
            throw new NotAuthorizedException("Bearer");
        }

    }

    private boolean verifyCode(UserModel user, VerifyCodeRequest verifyCodeRequest) {
        if (verifyCodeRequest.getSessionToken() == null) {
            return false;
        }
        Long userID = Long.parseLong(user.getFirstAttribute("user_id"));
        GraphQLClient graphQLClient = new GraphQLClient(TokenUtil.getAdminAccessToken(session));
        return graphQLClient.verifyTwoFactorAuth(userID, verifyCodeRequest.getCode(), verifyCodeRequest.getSessionToken());
    }


    @OPTIONS
    @Path("login")
    public Response handleLoginCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("logout")
    public Response handleLogoutCors() {
        return handleCores().build();
    }


    @Path("otp-login")
    public Response handleOtpLoginCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("impersonate-provider-user")
    public Response handleImpersonateProviderUserCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("impersonate-user")
    public Response handleImpersonateUserCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("impersonate-customer-user")
    public Response handleImpersonateCustomerUserCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("impersonate")
    public Response handleImpersonateCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("verify-code")
    public Response handleVerifyCodeCors() {
        return handleCores().build();
    }

    @OPTIONS
    @Path("biometric-login")
    public Response handleBiometricLoginCors() {
        return handleCores().build();
    }

    private Response.ResponseBuilder handleCores() {
        return Response
                .status(Response.Status.NO_CONTENT)
                .header("Access-Control-Allow-Origin", "*")
                .header("Access-Control-Allow-Credentials", "true")
                .header("Access-Control-Allow-Headers",
                        "origin, content-type, accept, authorization")
                .header("Access-Control-Allow-Methods",
                        "POST, OPTIONS, HEAD");
    }

    private AuthResult checkAuth() {
        AuthResult auth = new AppAuthManager.BearerTokenAuthenticator(session).authenticate();
        if (auth == null) {
            throw new NotAuthorizedException("Bearer");
        }
        return auth;
    }
}