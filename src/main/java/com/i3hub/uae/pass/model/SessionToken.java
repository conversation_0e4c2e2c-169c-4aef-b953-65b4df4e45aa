package com.i3hub.uae.pass.model;

import java.util.Date;

public class SessionToken {
    private String userId;
    private Date expiry;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public Date getExpiry() {
        return expiry;
    }

    public void setExpiry(Long expiry) {
        this.expiry = new Date(expiry);
    }

    @Override
    public String toString() {
        return "SessionToken{" +
                "userId='" + userId + '\'' +
                ", expiry=" + expiry +
                '}';
    }
}
