package com.i3hub.uae.pass.client;


import com.apollographql.apollo.ApolloCall;
import com.apollographql.apollo.ApolloClient;
import com.apollographql.apollo.api.Error;
import com.apollographql.apollo.api.*;
import com.apollographql.apollo.exception.ApolloException;
import com.i3hub.uae.pass.model.BiometricLoginRequest;
import com.i3hub.uae.pass.mutations.BiometricLoginMutation;
import com.i3hub.uae.pass.mutations.PatientCreateMutation;
import com.i3hub.uae.pass.mutations.RequestOTPMutation;
import com.i3hub.uae.pass.mutations.VerifyTwoFactorAuthMutation;
import com.i3hub.uae.pass.model.OtpLoginRequest;
import com.i3hub.uae.pass.mutations.*;
import com.i3hub.uae.pass.queries.FeatureFlagQuery;
import com.i3hub.uae.pass.type.BiometricLoginInput;
import com.i3hub.uae.pass.type.*;
import com.i3hub.uae.pass.util.Base64Util;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import org.jboss.logging.Logger;
import org.jetbrains.annotations.NotNull;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

public class GraphQLClient {
    private ApolloClient apolloClient;
    private static final Logger LOGGER = Logger.getLogger(GraphQLClient.class.getName());

    public GraphQLClient() {
        this.apolloClient = ApolloClient.builder().serverUrl(System.getenv("GATEWAY")).build();
    }

    public GraphQLClient(String token) {


        OkHttpClient httpClient = new OkHttpClient.Builder()
                .connectTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .addInterceptor(chain -> {
                    Request original = chain.request();
                    Request.Builder builder = original.newBuilder().method(original.method(), original.body());
                    if (token != null) {
                        builder.header("Authorization", "JWT " + token);
                    }
                    return chain.proceed(builder.build());
                })
                .build();
        this.apolloClient = ApolloClient.builder().serverUrl(System.getenv("GATEWAY")).okHttpClient(httpClient).build();
    }

    private Operation.Data executeQuery(Query query) {
        CompletableFuture<Operation.Data> future = new CompletableFuture<>();

        apolloClient.query(query).enqueue(new ApolloCall.Callback<Optional<Operation.Data>>() {
            @Override
            public void onResponse(@NotNull Response<Optional<Operation.Data>> response) {
                if (response.hasErrors()) {
                    String errors = response.getErrors().stream()
                            .map(x -> x.getMessage())
                            .collect(Collectors.joining(", "));
                    future.completeExceptionally(new ApolloException(errors));
                    return;
                }

                future.complete(response.getData().orElse(null));
            }

            @Override
            public void onFailure(@NotNull ApolloException e) {
                future.completeExceptionally(e);
            }
        });

        return Mono.fromFuture(future).block();
    }

    private Operation.Data executeMutation(Mutation mutation) {
        CompletableFuture<Operation.Data> future = new CompletableFuture<>();
        apolloClient.mutate(mutation).enqueue(new ApolloCall.Callback<Optional<Operation.Data>>() {
            @Override
            public void onResponse(@NotNull Response<Optional<Operation.Data>> response) {
                LOGGER.info(response);
                if (response.getData().isPresent()) {
                    future.complete(response.getData().orElse(null));
                } else {
                    String errors = response.getErrors().stream()
                            .map(Error::getMessage)
                            .collect(Collectors.joining(", "));
                    LOGGER.info(errors);
                    future.completeExceptionally(new ApolloException(errors));
                }
            }

            @Override
            public void onFailure(@NotNull ApolloException e) {
                future.completeExceptionally(e);
            }
        });

        return Mono.fromFuture(future).block();
    }

    public Boolean isCustomerRegistrationEnabled() {
        FeatureFlagQuery featureFlag = FeatureFlagQuery.builder().build();
        FeatureFlagQuery.Data result = (FeatureFlagQuery.Data) executeQuery(featureFlag);
        if (result.getFeatureFlag().isPresent()) {
            return result.getFeatureFlag().get().isCustomerRegistrationFeatureEnabled();
        } else {
            return false;
        }
    }


    public List<PatientCreateMutation.PatientError> patientCreate(PatientInput patientInput) {
        PatientCreateMutation patientCreateMutation = PatientCreateMutation.builder()
                .input(patientInput)
                .build();
        PatientCreateMutation.Data result = (PatientCreateMutation.Data) executeMutation(patientCreateMutation);
        return result.getPatientCreate().get().getPatientErrors();
    }


    public RequestOTPMutation.RequestOtp requestOtp(String verificationMethod, String phoneNumber, String email) {
        RequestOTPMutation requestOTPMutation = RequestOTPMutation.builder()
                .phoneNumber(verificationMethod.equals("MOBILE") ? phoneNumber : null)
                .email(verificationMethod.equals("EMAIL") ? email : null)
                .operation(OTPOperationEnum.TWO_FACTOR_AUTHENTICATION)
                .build();
        RequestOTPMutation.Data result = (RequestOTPMutation.Data) executeMutation(requestOTPMutation);
        if (!result.getRequestOtp().get().getAccountErrors().isEmpty()) {
            throw new RuntimeException(result.getRequestOtp().get().getAccountErrors().get(0).getMessage().get());
        }
        if (!result.getRequestOtp().isPresent()) {
            throw new RuntimeException("Invalid OTP");
        }
        return result.getRequestOtp().get();
    }


    public Boolean verifyTwoFactorAuth(Long userId, String code, String sessionToken) {
        VerifyTwoFactorAuthMutation verifyTwoFactorAuthMutation = VerifyTwoFactorAuthMutation.builder()
                .input(VerifyTwoFactorAuthInput.builder()
                        .userId(Base64Util.geTypeId("User", userId))
                        .code(code)
                        .sessionToken(sessionToken)
                        .build())
                .build();
        VerifyTwoFactorAuthMutation.Data result = (VerifyTwoFactorAuthMutation.Data) executeMutation(verifyTwoFactorAuthMutation);
        if (!result.getVerifyTwoFactorAuth().isPresent()) {
            throw new RuntimeException("Invalid OTP");
        }
        if (!result.getVerifyTwoFactorAuth().get().getAccountErrors().isEmpty()) {
            throw new RuntimeException(result.getVerifyTwoFactorAuth().get().getAccountErrors().get(0).getMessage().get());
        }
        return result.getVerifyTwoFactorAuth().get().getSuccess().get();
    }

    public Boolean verifyOtpForLogin(OtpLoginRequest request) {
        VerifyOtpForLoginMutation verifyTwoFactorAuthMutation = VerifyOtpForLoginMutation.builder()
                .input(VerifyOTPForLoginInput.builder()
                        .code(request.getCode())
                        .email(request.getEmail())
                        .mobile(request.getMobile())
                        .sessionToken(request.getSessionToken())
                        .build())
                .build();
        VerifyOtpForLoginMutation.Data result = (VerifyOtpForLoginMutation.Data) executeMutation(verifyTwoFactorAuthMutation);
        if (!result.getVerifyOtpForLogin().isPresent()) {
            LOGGER.info("Invalid OTP");
            return false;
        }
        if (!result.getVerifyOtpForLogin().get().getAccountErrors().isEmpty()) {
            LOGGER.info("Error: " + result.getVerifyOtpForLogin().get().getAccountErrors().get(0).getMessage().get());
            return false;
        }
        return result.getVerifyOtpForLogin().get().getSuccess().get();
    }


    public Long biometricLogin(BiometricLoginRequest request) {
        BiometricLoginInput input = BiometricLoginInput.builder()
                .deviceId(request.getDeviceId())
                .signature(request.getSignature())
                .payload(request.getPayload())
                .build();
        BiometricLoginMutation biometricLoginMutation = BiometricLoginMutation.builder()
                .input(input).build();
        BiometricLoginMutation.Data result = (BiometricLoginMutation.Data) executeMutation(biometricLoginMutation);
        if (!result.getBiometricLogin().isPresent()) {
            return null;
        } else if (!result.getBiometricLogin().get().getAccountErrors().isEmpty()) {
            return null;
        } else if (!result.getBiometricLogin().get().getSuccess().isPresent() || !result.getBiometricLogin().get().getSuccess().get()) {
            return null;
        } else if (result.getBiometricLogin().get().getUserId().isPresent()) {
            return Base64Util.geTypePK(result.getBiometricLogin().get().getUserId().get());
        } else {
            return null;
        }
    }


}
