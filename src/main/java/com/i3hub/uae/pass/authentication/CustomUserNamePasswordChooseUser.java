package com.i3hub.uae.pass.authentication;

import com.i3hub.uae.pass.util.UserUtil;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.Authenticator;
import org.keycloak.authentication.authenticators.browser.AbstractUsernameFormAuthenticator;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.services.ServicesLogger;

import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.util.ArrayList;
import java.util.List;

public class CustomUserNamePasswordChooseUser extends AbstractUsernameFormAuthenticator implements Authenticator {
    protected static ServicesLogger log;


    public CustomUserNamePasswordChooseUser() {
    }

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        List<UserModel> users = new ArrayList<>();
        users.add(context.getUser());
        users.addAll(UserUtil.getChildUsers(context, context.getUser().getFirstAttribute("user_id")));
        if (users.size() == 1) {
            context.clearUser();
            UserModel user = users.get(0);
            context.setUser(user);
            context.success();
        } else {
            Response challenge = context.form().setAttribute("users", users).createForm("uae_login.ftl");
            context.challenge(challenge);
        }
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        if (formData.containsKey("cancel")) {
            context.cancelLogin();
        } else {
            selectUser(context, formData);
            context.success();
        }
    }

    protected void selectUser(AuthenticationFlowContext context, MultivaluedMap<String, String> formData) {
        String userId = formData.getFirst("context");
        if (UserUtil.getChildUsers(context,context.getUser().getFirstAttribute("user_id")).stream().anyMatch(x -> x.getId().equals(userId)) ||
                context.getUser().getId().equals(userId)) {
            context.clearUser();
            UserModel user = UserUtil.getUserById(context.getSession(), userId);
            context.setUser(user);
        } else {
            throw new RuntimeException("User does not belong to the current user");
        }
    }


    public boolean requiresUser() {
        return false;
    }

    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
    }

    public void close() {
    }

    static {
        log = ServicesLogger.LOGGER;
    }
}
