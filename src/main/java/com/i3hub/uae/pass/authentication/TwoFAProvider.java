package com.i3hub.uae.pass.authentication;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.i3hub.uae.pass.client.GraphQLClient;
import com.i3hub.uae.pass.model.OtpLoginRequest;
import com.i3hub.uae.pass.model.TwoFAResponse;
import com.i3hub.uae.pass.mutations.RequestOTPMutation;
import com.i3hub.uae.pass.util.Base64Util;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import org.jboss.logging.Logger;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.Authenticator;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class TwoFAProvider implements Authenticator {
    private static final Logger LOGGER = Logger.getLogger(TwoFAProvider.class.getName());

    public TwoFAProvider() {

    }

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        GraphQLClient graphQLClient = new GraphQLClient();
        TwoFAResponse twoFAResponse = handleTFA(graphQLClient, context.getUser());
        if (twoFAResponse == null) {
            context.success();
        } else {
            context.getAuthenticationSession().setAuthNote("AUTH_METHOD", twoFAResponse.getVerificationMethod());
            context.getAuthenticationSession().setAuthNote("SESSION_TOKEN", twoFAResponse.getSessionToken());
            context.getAuthenticationSession().setAuthNote("CONTACT_INFO",twoFAResponse.getVerificationMethod().equalsIgnoreCase("mobile")?
                    context.getUser().getFirstAttribute("mobile"):context.getUser().getEmail());


            context.form().setAttribute("AUTH_METHOD", twoFAResponse.getVerificationMethod());
            context.form().setAttribute("SESSION_TOKEN", twoFAResponse.getSessionToken());
            context.form().setAttribute("CONTACT_INFO",twoFAResponse.getVerificationMethod().equalsIgnoreCase("mobile")?
                    context.getUser().getFirstAttribute("mobile"):context.getUser().getEmail());

            Response challenge = context.form().createForm("otp.ftl");
            context.challenge(challenge);
        }
    }

    @Override
    public void action(AuthenticationFlowContext context) {
        MultivaluedMap<String, String> formData = context.getHttpRequest().getDecodedFormParameters();
        String otp = formData.getFirst("otp");

        if (isValidOtp(context, context.getUser(), otp)) {
            context.success();
        } else {
            Response challenge = context.form()
                    .setError("Invalid OTP. Please try again.")
                    .setAttribute("AUTH_METHOD", context.getAuthenticationSession().getAuthNote("AUTH_METHOD"))
                    .setAttribute("SESSION_TOKEN",context.getAuthenticationSession().getAuthNote("SESSION_TOKEN"))
                    .setAttribute("CONTACT_INFO",context.getAuthenticationSession().getAuthNote("CONTACT_INFO"))
                    .createForm("otp.ftl");
            context.failureChallenge(org.keycloak.authentication.AuthenticationFlowError.INVALID_CREDENTIALS, challenge);
        }
    }


    public boolean requiresUser() {
        return false;
    }

    public boolean configuredFor(KeycloakSession session, RealmModel realm, UserModel user) {
        return true;
    }

    public void setRequiredActions(KeycloakSession session, RealmModel realm, UserModel user) {
    }

    public void close() {
    }


    private TwoFAResponse handleTFA(GraphQLClient graphQLClient, UserModel user) {
        TwoFAResponse twoFAResponse = new TwoFAResponse();
        twoFAResponse.setUserId(user.getId());
        List<String> twoFaEnabled = user.getAttributes().getOrDefault("two_factor_auth_enabled", new ArrayList<>());
        if (!twoFaEnabled.isEmpty() && Boolean.parseBoolean(twoFaEnabled.get(0))) {
            String twoFaMethod = user.getFirstAttribute("two_factor_auth_verification_method").toUpperCase();
            twoFAResponse.setVerificationMethod(twoFaMethod.toUpperCase());
            RequestOTPMutation.RequestOtp requestOtp = null;
            switch (twoFaMethod) {
                case "EMAIL":
                case "MOBILE":
                    requestOtp = graphQLClient.requestOtp(twoFaMethod, user.getFirstAttribute("mobile"), user.getEmail());
                    twoFAResponse.setSessionToken(requestOtp.getSessionToken().get());
                    break;
                case "AUTHENTICATOR":
                    twoFAResponse.setSessionToken(Base64Util.encode(UUID.randomUUID().toString()));
                    break;
            }
            if (requestOtp != null) {
                twoFAResponse.setSessionToken(requestOtp.getSessionToken().get());
            }
            return twoFAResponse;
        } else {
            return null;
        }
    }

    private boolean isValidOtp(AuthenticationFlowContext context, UserModel user, String otp) {
        String sessionToken = context.getAuthenticationSession().getAuthNote("SESSION_TOKEN");
        Long userID = Long.parseLong(user.getFirstAttribute("user_id"));
        GraphQLClient graphQLClient = new GraphQLClient();
        return graphQLClient.verifyTwoFactorAuth(userID, otp, sessionToken);
    }
}
