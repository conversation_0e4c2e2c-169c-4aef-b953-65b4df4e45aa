package com.i3hub.uae.pass.authentication;

import com.i3hub.uae.pass.client.GraphQLClient;
import com.i3hub.uae.pass.mutations.PatientCreateMutation;
import com.i3hub.uae.pass.type.PatientInput;
import com.i3hub.uae.pass.util.UserUtil;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.authentication.authenticators.broker.AbstractIdpAuthenticator;
import org.keycloak.authentication.authenticators.broker.util.SerializedBrokeredIdentityContext;
import org.keycloak.broker.provider.BrokeredIdentityContext;
import org.keycloak.models.KeycloakSession;
import org.keycloak.models.RealmModel;
import org.keycloak.models.UserModel;
import org.keycloak.services.ServicesLogger;
import org.keycloak.sessions.AuthenticationSessionModel;

import jakarta.ws.rs.core.Response;
import java.util.List;
import java.util.stream.Collectors;

import static org.keycloak.authentication.authenticators.broker.util.PostBrokerLoginConstants.PBL_BROKERED_IDENTITY_CONTEXT;

public class SocialLogin extends AbstractIdpAuthenticator {
    protected static ServicesLogger log;


    public SocialLogin() {
    }

    private BrokeredIdentityContext getBrokeredIdentityContext(AuthenticationFlowContext context) {
        AuthenticationSessionModel authSession = context.getAuthenticationSession();
        SerializedBrokeredIdentityContext serializedCtx = SerializedBrokeredIdentityContext.readFromAuthenticationSession(context.getAuthenticationSession(), BROKERED_CONTEXT_NOTE);
        if (serializedCtx == null) {
            serializedCtx = SerializedBrokeredIdentityContext.readFromAuthenticationSession(context.getAuthenticationSession(), PBL_BROKERED_IDENTITY_CONTEXT);
        }
        return serializedCtx.deserialize(context.getSession(), authSession);
    }

    @Override
    protected void authenticateImpl(AuthenticationFlowContext context, SerializedBrokeredIdentityContext serializedBrokeredIdentityContext, BrokeredIdentityContext brokeredIdentityContext) {
        KeycloakSession session = context.getSession();
        List<UserModel> users = UserUtil.getUsers(session, getBrokeredIdentityContext(context).getEmail());
        if (users.isEmpty() && context.getSession().getContext().getClient().getClientId().equals("customer")) {
            GraphQLClient graphQLClient = new GraphQLClient(com.i3hub.uae.pass.util.TokenUtil.getAdminAccessToken(context.getSession()));
            if(!graphQLClient.isCustomerRegistrationEnabled()) {
                userNotFoundError(context);
                return;
            }
            List<PatientCreateMutation.PatientError> errors = graphQLClient.patientCreate(PatientInput.builder()
                    .firstName(getBrokeredIdentityContext(context).getFirstName())
                    .lastName(getBrokeredIdentityContext(context).getLastName())
                    .email(getBrokeredIdentityContext(context).getEmail())
                    .nationalIdNumber(getBrokeredIdentityContext(context).getUserAttribute("national_id"))
                    .build());
            if (!errors.isEmpty()) {
                handleError(context, errors.stream().map(error -> error.getMessage().get()).collect(Collectors.joining(", ")));
            }
            session = UserUtil.getNewSession(session);
            users = UserUtil.getUsers(session, getBrokeredIdentityContext(context).getEmail());
            for(UserModel user: users){
                UserUtil.joinPatientGroup(session, user);
            }
        }
        if (users.isEmpty()) {
            userNotFoundError(context);
        } else {
            context.clearUser();
            UserModel user = users.get(0);
            context.setUser(user);
            context.success();
        }
    }

    @Override
    public void authenticate(AuthenticationFlowContext context) {
        authenticateImpl(context, null, null);
    }

    @Override
    protected void actionImpl(AuthenticationFlowContext authenticationFlowContext, SerializedBrokeredIdentityContext serializedBrokeredIdentityContext, BrokeredIdentityContext brokeredIdentityContext) {

    }

    void handleError(AuthenticationFlowContext context, String error) {
        Response challengeResponse = context.form().setError(error, new Object[]{}).createErrorPage(Response.Status.NOT_FOUND);
        context.challenge(challengeResponse);
    }

    void userNotFoundError(AuthenticationFlowContext context) {
        Response challengeResponse = context.form().setError("User not found", new Object[]{}).createErrorPage(Response.Status.NOT_FOUND);
        context.challenge(challengeResponse);
    }

    @Override
    public boolean requiresUser() {
        return false;
    }

    @Override
    public boolean configuredFor(KeycloakSession keycloakSession, RealmModel realmModel, UserModel userModel) {
        return true;
    }

    static {
        log = ServicesLogger.LOGGER;
    }
}
