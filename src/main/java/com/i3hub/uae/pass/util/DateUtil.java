package com.i3hub.uae.pass.util;

import org.apache.commons.lang3.time.DateUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

public class DateUtil {
    public static final List<String> dateTimesFormats = Arrays.asList(
            "dd/MMM/yyyy",
            "dd/MMM/yyyy HH:mm:ss",
            "MMM/dd/yyyy",
            "yyyy/MMM/dd",
            "yyyy/dd/MMM",
            "HH:mm",
            "MMddyyyy",
            "MMM dd, yyyy",
            "dd-MMM-yyyy",
            "MMM-dd-yyyy",
            "yyyy-MMM-dd",
            "yyyy-dd-MMM",
            "dd-MM-yy",
            "dd-MM-yy HH",
            "dd-MM-yy HH:mm",
            "dd-MM-yy HH:mm:ss",
            "dd-MM-yyyy",
            "dd-MM-yyyy HH",
            "dd-MM-yyyy HH:mm",
            "dd-MM-yyyy HH:mm:ss",
            "yy-MM-dd",
            "yy-MM-dd HH",
            "yy-MM-dd HH:mm",
            "yy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy-MM-dd HH",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'hh:mm:ss'Z'",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ssX",
            "yyyy-MM-dd'T'HH:mm:ssXX",
            "yyyy-MM-dd'T'HH:mm:ssXXX",
            "yyyy-MM-dd'T'HH:mm:ss X",
            "yyyy-MM-dd'T'HH:mm:ss XX",
            "yyyy-MM-dd'T'HH:mm:ss XXX",
            "yyyy-MM-dd HH:mm:ss.S",
            "yyyy-MM-dd'T'hh:mm:ss.S'Z'",
            "yyyy-MM-dd'T'HH:mm:ss.S",
            "yyyy-MM-dd'T'HH:mm:ss.SX",
            "yyyy-MM-dd'T'HH:mm:ss.SXX",
            "yyyy-MM-dd'T'HH:mm:ss.SXXX",
            "yyyy-MM-dd'T'HH:mm:ss.S X",
            "yyyy-MM-dd'T'HH:mm:ss.S XX",
            "yyyy-MM-dd'T'HH:mm:ss.S XXX",
            "yy/MM/dd",
            "yy/MM/dd HH",
            "yy/MM/dd HH:mm",
            "yy/MM/dd HH:mm:ss",
            "yyyy/MM/dd",
            "yyyy/MM/dd HH",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM/dd HH:mm:ss",
            "dd/MM/yy",
            "dd/MM/yy HH",
            "dd/MM/yy HH:mm",
            "dd/MM/yy HH:mm:ss",
            "dd/MM/yyyy",
            "dd/MM/yyyy HH",
            "dd/MM/yyyy HH:mm",
            "dd/MM/yyyy HH:mm:ss",
            "dd/MM/yyyy HH:mm:ss Z",
            "dd/MM/yyyy HH:mm:ssX",
            "dd/MM/yyyy HH:mm:ssXX",
            "dd/MM/yyyy HH:mm:ssXXX",
            "dd/MM/yyyy HH:mm:ss X",
            "dd/MM/yyyy HH:mm:ss XX",
            "dd/MM/yyyy HH:mm:ss XXX",
            "dd/MM/yyyy HH:mm:ss.S Z",
            "dd/MM/yyyy HH:mm:ss.S",
            "dd/MM/yyyy HH:mm:ss.SX",
            "dd/MM/yyyy HH:mm:ss.SXX",
            "dd/MM/yyyy HH:mm:ss.SXXX",
            "dd/MM/yyyy HH:mm:ss.S X",
            "dd/MM/yyyy HH:mm:ss.S XX",
            "dd/MM/yyyy HH:mm:ss.S XXX",
            "dd-MM-yyyy HH:mm:ss",
            "dd-MMM-yyyy HH:mm:ss",
            "E MMM dd HH:mm:ss Z yyyy"
    );

    public static Date parseDate(Object object) {
        Date dateInstance = null;

        if (object instanceof Date) {
            dateInstance = (Date) object;
        } else {
            String dateStr = "" + object;
            try {
                dateInstance = DateUtils.parseDateStrictly(dateStr, dateTimesFormats.toArray(new String[0]));
            } catch (Exception exception) {
            }
        }
        return dateInstance;
    }

    public static Double calculateAge(Date date) {
        LocalDate currentDate = LocalDate.now();
        if ((date != null) && (currentDate != null)) {
            LocalDate birthDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
            Period period = Period.between(birthDate, currentDate);
            BigDecimal years = BigDecimal.valueOf(period.getYears());
            BigDecimal months = BigDecimal.valueOf(period.getMonths()).divide(BigDecimal.valueOf(12), 5, RoundingMode.HALF_UP);
            BigDecimal days = BigDecimal.valueOf(period.getDays()).divide(BigDecimal.valueOf(365), 5, RoundingMode.HALF_UP);
            return years.add(months).add(days).doubleValue();
        } else {
            return null;
        }
    }
}
