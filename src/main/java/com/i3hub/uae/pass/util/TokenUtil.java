package com.i3hub.uae.pass.util;

import org.keycloak.OAuth2Constants;
import org.keycloak.events.Details;
import org.keycloak.events.EventBuilder;
import org.keycloak.models.*;
import org.keycloak.protocol.oidc.OIDCLoginProtocol;
import org.keycloak.protocol.oidc.TokenManager;
import org.keycloak.representations.AccessTokenResponse;
import org.keycloak.services.Urls;
import org.keycloak.services.managers.AuthenticationManager;
import org.keycloak.services.managers.AuthenticationSessionManager;
import org.keycloak.sessions.AuthenticationSessionModel;
import org.keycloak.sessions.RootAuthenticationSessionModel;

import static com.i3hub.uae.pass.util.AppType.appTypeMap;

public class TokenUtil {
    public static AccessTokenResponse getAccessToken(UserModel targetUser, KeycloakSession session, boolean removeSession) {
        RealmModel realm = session.getContext().getRealm();
        ClientModel targetClient = session.clients().getClientByClientId(realm, appTypeMap.getOrDefault(targetUser.getFirstAttribute("app_type"), "account-console"));
        org.keycloak.protocol.oidc.TokenManager tokenManager = new org.keycloak.protocol.oidc.TokenManager();
        UserSessionModel targetUserSession = session.sessions().createUserSession(session.getContext().getRealm(), targetUser, targetUser.getUsername(), session.getContext().getConnection().getRemoteAddr(), "impersonate", false, null, null);
        EventBuilder event = new EventBuilder(session.getContext().getRealm(), session, session.getContext().getConnection());
        RootAuthenticationSessionModel rootAuthSession = new AuthenticationSessionManager(session).createAuthenticationSession(realm, false);
        AuthenticationSessionModel authSession = rootAuthSession.createAuthenticationSession(targetClient);
        authSession.setAuthenticatedUser(targetUser);
        authSession.setProtocol(OIDCLoginProtocol.LOGIN_PROTOCOL);
        authSession.setClientNote(OIDCLoginProtocol.ISSUER, Urls.realmIssuer(session.getContext().getUri().getBaseUri(), realm.getName()));
        event.session(targetUserSession);
        AuthenticationManager.setClientScopesInSession(authSession);
        ClientSessionContext clientSessionCtx = org.keycloak.protocol.oidc.TokenManager.attachAuthenticationSession(session, targetUserSession, authSession);
        TokenManager.AccessTokenResponseBuilder responseBuilder = tokenManager.responseBuilder(realm, targetClient, event, session, targetUserSession, clientSessionCtx)
                .generateAccessToken();
        responseBuilder.getAccessToken().issuedFor(targetClient.getClientId());
        responseBuilder.generateRefreshToken();
        responseBuilder.getRefreshToken().issuedFor(targetClient.getClientId());
        responseBuilder.generateIDToken().generateAccessTokenHash();
        AccessTokenResponse res = responseBuilder.build();
        event.detail(Details.AUDIENCE, targetClient.getClientId());
        event.success();
        if (removeSession)
            session.sessions().removeUserSession(realm, targetUserSession);
        return res;
    }

    public static String getAdminAccessToken(KeycloakSession keycloakSession) {
        UserModel user = keycloakSession.users().getUserByUsername(keycloakSession.getContext().getRealm(), System.getenv("ADMIN_USER"));
        return getAccessToken(user, keycloakSession, true).getToken();
    }

    public static AccessTokenResponse impersonate(KeycloakSession session,UserSessionModel currentUserSession, UserModel targetUser, boolean removeSession) {
        RealmModel realm = session.getContext().getRealm();
        ClientModel targetClient = session.clients().getClientByClientId(realm, appTypeMap.getOrDefault(targetUser.getFirstAttribute("app_type"), "account-console"));
        org.keycloak.protocol.oidc.TokenManager tokenManager = new org.keycloak.protocol.oidc.TokenManager();
        UserSessionModel targetUserSession = session.sessions().createUserSession(session.getContext().getRealm(), targetUser, targetUser.getUsername(), session.getContext().getConnection().getRemoteAddr(), "impersonate", false, null, null);
        EventBuilder event = new EventBuilder(session.getContext().getRealm(), session, session.getContext().getConnection());
        RootAuthenticationSessionModel rootAuthSession = new AuthenticationSessionManager(session).createAuthenticationSession(realm, false);
        AuthenticationSessionModel authSession = rootAuthSession.createAuthenticationSession(targetClient);
        authSession.setAuthenticatedUser(targetUser);
        authSession.setProtocol(OIDCLoginProtocol.LOGIN_PROTOCOL);
        authSession.setClientNote(OIDCLoginProtocol.ISSUER, Urls.realmIssuer(session.getContext().getUri().getBaseUri(), realm.getName()));
        event.session(targetUserSession);
        AuthenticationManager.setClientScopesInSession(authSession);
        ClientSessionContext clientSessionCtx = org.keycloak.protocol.oidc.TokenManager.attachAuthenticationSession(session, targetUserSession, authSession);
        TokenManager.AccessTokenResponseBuilder responseBuilder = tokenManager.responseBuilder(realm, targetClient, event, session, targetUserSession, clientSessionCtx)
                .generateAccessToken();
        responseBuilder.getAccessToken().issuedFor(targetClient.getClientId());
        responseBuilder.generateRefreshToken();
        responseBuilder.getRefreshToken().issuedFor(targetClient.getClientId());
        responseBuilder.generateIDToken().generateAccessTokenHash();
        AccessTokenResponse res = responseBuilder.build();
        event.detail(Details.AUDIENCE, targetClient.getClientId());
        event.success();
        if (removeSession)
            session.sessions().removeUserSession(realm, currentUserSession);
        return res;
    }
}
