package com.i3hub.uae.pass.util;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Base64Util {

    public static Long geTypePK(String base64) {
        if (base64 == null) {
            return null;
        }
        try {
            return Long.parseLong(new String(Base64.getDecoder().decode(base64), StandardCharsets.UTF_8).split(":")[1]);
        } catch (Exception ex) {
            return Long.parseLong(base64);
        }
    }

    public static String geTypeId(String type, Long userId) {
        return Base64.getEncoder().encodeToString(String.format("%s:%s", type, userId).getBytes());
    }

    public static String encode(String value) {
        return Base64.getEncoder().encodeToString(value.getBytes());
    }
}
