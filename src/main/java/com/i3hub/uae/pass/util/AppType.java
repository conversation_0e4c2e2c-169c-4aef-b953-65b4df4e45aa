package com.i3hub.uae.pass.util;

import java.util.HashMap;
import java.util.Map;

public class AppType {
    public static Map<String, String> clientMap = new HashMap<String, String>() {{
        put("admin", "Admin");
        put("customer", "Customer");
        put("vendor", "Vendor");
        put("payer", "Payer");
        put("aggregator", "Aggregator");
    }};

    public static Map<String, String> appTypeMap = new HashMap<String, String>() {{
        put("Admin", "admin");
        put("Customer", "customer");
        put("Vendor", "vendor");
        put("Payer", "payer");
        put("Aggregator", "aggregator");
    }};
}
