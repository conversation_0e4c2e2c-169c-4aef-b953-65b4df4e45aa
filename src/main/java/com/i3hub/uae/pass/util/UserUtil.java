package com.i3hub.uae.pass.util;

import com.i3hub.uae.pass.model.LoginRequest;
import org.keycloak.OAuthErrorException;
import org.keycloak.authentication.AuthenticationFlowContext;
import org.keycloak.broker.provider.BrokeredIdentityContext;
import org.keycloak.models.*;
import org.keycloak.services.CorsErrorResponseException;
import org.keycloak.services.resources.Cors;

import jakarta.ws.rs.BadRequestException;
import jakarta.ws.rs.NotAuthorizedException;
import jakarta.ws.rs.core.Response;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

public class UserUtil {

    public static KeycloakSession getNewSession(KeycloakSession session) {
        session.getTransactionManager().commit();
        KeycloakSession newSession = session.getKeycloakSessionFactory().create();
        newSession.getContext().setRealm(session.getContext().getRealm());
        newSession.getContext().setClient(session.getContext().getClient());
        newSession.getContext().setAuthenticationSession(session.getContext().getAuthenticationSession());
        newSession.getTransactionManager().begin();
        return newSession;
    }

    public static void joinPatientGroup(KeycloakSession session, UserModel user) {
        GroupModel patientGroup =getGroup(session, "patient");
        if (patientGroup != null) {
            user.joinGroup(patientGroup);
        }
    }

    public static UserModel getUser(KeycloakSession session, LoginRequest loginRequest) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("app_type", loginRequest.getAppType());
        if (loginRequest.getUsername().startsWith("+")) {
            query.put("mobile", loginRequest.getUsername());
        } else if (loginRequest.getUsername().contains("@")) {
            query.put(UserModel.EMAIL, loginRequest.getUsername());
        } else {
            query.put("national_id", loginRequest.getUsername());
        }

        if (loginRequest.getVendorId() != null) {
            query.put("vendor_id", String.valueOf(loginRequest.getVendorId()));
        }

        List<UserModel> result = session.users().searchForUserStream(session.getContext().getRealm(), query).collect(Collectors.toList());

        if (result.size() != 1) {
            throw new NotAuthorizedException("Bearer");
        } else {
            return result.get(0);

        }
    }


    public static UserModel findByProvider(Cors cors, KeycloakSession session, KeycloakContext keycloakContext, BrokeredIdentityContext context, FederatedIdentityModel federatedIdentityModel) {
        if (federatedIdentityModel.getIdentityProvider().equals("uae_pass")) {
            if (context.getUserAttribute("national_id") != null) {
                return getUsers(session, keycloakContext, context.getUserAttribute("national_id")).stream().findFirst().orElse(null);
            } else {
                throw new CorsErrorResponseException(cors, OAuthErrorException.INVALID_REQUEST, "UAE user not verified", Response.Status.BAD_REQUEST);

            }
        } else {
            if (context.getEmail() != null) {
                return getUsers(session, keycloakContext, context.getEmail()).stream().findFirst().orElse(null);
            } else {
                throw new CorsErrorResponseException(cors, OAuthErrorException.INVALID_REQUEST, "Email not permitted to read", Response.Status.BAD_REQUEST);
            }
        }
    }

    public static boolean validateCredentials(UserModel user, String password) {
        return user.credentialManager().isValid(UserCredentialModel.password(password));
    }


    public static List<UserModel> getUsers(KeycloakSession session, String userName) {
        return getUsers(session, session.getContext(), userName);
    }

    public static List<UserModel> getUsers(KeycloakSession session, KeycloakContext context, String userName) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("app_type", AppType.clientMap.getOrDefault(context.getClient().getClientId(), "account-console"));
        if (userName.startsWith("+")) {
            query.put("mobile", userName);
        } else if (userName.contains("@")) {
            query.put("email", userName);
        } else {
            query.put("national_id", userName);
        }
        List<UserModel> result = session.users().searchForUserStream(context.getRealm(), query).collect(Collectors.toList());

        List<UserModel> users = new ArrayList<>();
        for (UserModel userModel : result) {
            List<String> dateOfBirthAttribute = userModel.getAttributes().getOrDefault("date_of_birth", new ArrayList<>());
            if (!dateOfBirthAttribute.isEmpty()) {
                Date dateOfBirth = DateUtil.parseDate(dateOfBirthAttribute.get(0));
                if (DateUtil.calculateAge(dateOfBirth) > 18) {
                    users.add(userModel);
                }
            } else {
                users.add(userModel);
            }
        }
        return users;
    }

    public static UserModel getUserById(KeycloakSession session, String id) {
        return session.users().getUserById(session.getContext().getRealm(), id);
    }

    public static List<UserModel> getChildUsers(AuthenticationFlowContext context, String userId) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("parent_user_id", userId);
        List<UserModel> result = context.getSession().users().searchForUserStream(context.getRealm(), query).collect(Collectors.toList());
        List<UserModel> users = new ArrayList<>();
        for (UserModel userModel : result) {
            List<String> dateOfBirthAttribute = userModel.getAttributes().getOrDefault("date_of_birth", new ArrayList<>());
            if (!dateOfBirthAttribute.isEmpty()) {
                Date dateOfBirth = DateUtil.parseDate(dateOfBirthAttribute.get(0));
                if (DateUtil.calculateAge(dateOfBirth) < 18) {
                    users.add(userModel);
                }
            }
        }
        return users;
    }

    public static boolean validateParentChildRelation(List<UserModel> users) {
        UserModel parent = users.stream().filter(x -> x.getFirstAttribute("parent_user_id") == null).findFirst().orElse(null);
        UserModel child = users.stream().filter(x -> x.getFirstAttribute("parent_user_id") != null).findFirst().orElse(null);
        if (parent != null && child != null) {
            return parent.getFirstAttribute("user_id").equals(child.getFirstAttribute("parent_user_id"));
        } else {
            return false;
        }
    }

    public static boolean validateChildsRelation(UserModel currentUser, UserModel requestedUser) {
        if (currentUser != null && requestedUser != null) {
            if (currentUser.getFirstAttribute("parent_user_id") != null && requestedUser.getFirstAttribute("parent_user_id") != null) {
                return currentUser.getFirstAttribute("parent_user_id").equals(requestedUser.getFirstAttribute("parent_user_id"));
            } else {
                return false;
            }
        } else {
            return false;
        }
    }


    public static UserModel getUserByUserId(KeycloakSession session, KeycloakContext context, String id) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("user_id", id);
        List<UserModel> result = session.users().searchForUserStream(context.getRealm(), query).collect(Collectors.toList());
        if (result.isEmpty()) {
            return null;
        } else {
            return result.get(0);
        }
    }

    public static Long decodeUserID(String userId) {
        if (userId == null || userId.isEmpty()) {
            throw new BadRequestException("userId is required");
        } else {
            return geTypePK(userId);
        }
    }

    public static long geTypePK(String base64) {
        try {
            return Long.parseLong(new String(Base64.getDecoder().decode(base64), StandardCharsets.UTF_8).split(":")[1]);
        } catch (Exception ex) {
            throw new BadRequestException("invalid userId");
        }
    }

    public static GroupModel getGroup(KeycloakSession session, String groupName) {
        return session.groups().searchForGroupByNameStream(session.getContext().getRealm(), groupName, true, 0, 1).findFirst().orElse(null);
    }

    public static UserModel getUserByEmail(KeycloakSession session, KeycloakContext context, String email) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("email", email);
        List<UserModel> result = session.users().searchForUserStream(context.getRealm(), query).collect(Collectors.toList());
        if (result.isEmpty()) {
            return null;
        } else {
            return result.get(0);
        }
    }

    public static UserModel getUserByMobile(KeycloakSession session, KeycloakContext context, String mobile) {
        Map<String, String> query = new HashMap<>();
        query.put(UserModel.EXACT, String.valueOf(true));
        query.put(UserModel.ENABLED, String.valueOf(true));
        query.put("mobile", mobile);
        List<UserModel> result = session.users().searchForUserStream(context.getRealm(), query).collect(Collectors.toList());
        if (result.isEmpty()) {
            return null;
        } else {
            return result.get(0);
        }
    }
}
