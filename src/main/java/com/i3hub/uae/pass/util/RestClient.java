package com.i3hub.uae.pass.util;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.i3hub.uae.pass.model.MeetoraLinkResponse;
import org.apache.commons.io.IOUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.message.BasicNameValuePair;
import org.jboss.logging.Logger;
import org.keycloak.models.KeycloakSession;

import jakarta.ws.rs.core.MediaType;
import jakarta.ws.rs.core.MultivaluedMap;
import jakarta.ws.rs.core.Response;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

public class RestClient {
    private static final String MESSANGER_WORK_FLOW_URL;
    private static final ObjectMapper OBJECT_MAPPER;
    private static final Logger LOGGER;
    private final static int TIME_OUT = 5000;

    static {
        OBJECT_MAPPER = new ObjectMapper();
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        MESSANGER_WORK_FLOW_URL = System.getenv("WORK_FLOW_URL") + "/messenger/authorize-by-token";
        LOGGER = Logger.getLogger(RestClient.class);
    }

    public Response tokenExchange(KeycloakSession keycloakSession, MultivaluedMap<String, String> formParams) {
        try {
            RequestConfig requestConfig = RequestConfig.custom()
                    .setConnectTimeout(TIME_OUT)
                    .setConnectionRequestTimeout(TIME_OUT)
                    .setSocketTimeout(TIME_OUT).build();
            URI postURI = new URI(String.format("http://localhost:9080/auth/realms/%s/protocol/openid-connect/token", keycloakSession.getContext().getRealm().getName()));
            HttpPost request = new HttpPost(postURI);
            request.addHeader("X-Forwarded-For", keycloakSession.getContext().getConnection().getRemoteAddr());
            request.addHeader("X-Real-IP", keycloakSession.getContext().getConnection().getRemoteAddr());
            request.addHeader("X-Forwarded-Host", keycloakSession.getContext().getUri().getRequestUri().getHost());
            request.addHeader("X-Forwarded-Proto", "https");
            List<NameValuePair> data = Arrays.asList((
                            new BasicNameValuePair("client_id", formParams.get("client_id").stream().findFirst().orElse(null))),
                    new BasicNameValuePair("client_secret", formParams.get("client_secret").stream().findFirst().orElse(null)),
                    new BasicNameValuePair("grant_type", formParams.get("grant_type").stream().findFirst().orElse(null)),
                    new BasicNameValuePair("subject_token_type", formParams.get("subject_token_type").stream().findFirst().orElse(null)),
                    new BasicNameValuePair("subject_issuer", formParams.get("subject_issuer").stream().findFirst().orElse(null)),
                    new BasicNameValuePair("subject_token", formParams.get("subject_token").stream().findFirst().orElse(null)),
                    new BasicNameValuePair("scope", formParams.get("scope").stream().findFirst().orElse(null))
            );
            request.setEntity(new UrlEncodedFormEntity(data));
            try (CloseableHttpClient client = HttpClientBuilder.create()
                    .setDefaultRequestConfig(requestConfig)
                    .build();
                 CloseableHttpResponse response = client
                         .execute(request)) {
                String res = IOUtils.toString(response.getEntity().getContent(), StandardCharsets.UTF_8);
                return Response.status((response.getStatusLine().getStatusCode())).entity(res).type(MediaType.APPLICATION_JSON_TYPE).build();
            }
        } catch (Throwable e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    public MeetoraLinkResponse getMeetoraMeetingLink(String token) throws URISyntaxException, IOException {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(TIME_OUT)
                .setConnectionRequestTimeout(TIME_OUT)
                .setSocketTimeout(TIME_OUT).build();

        URI postURI = new URI(MESSANGER_WORK_FLOW_URL);
        HttpPost request = new HttpPost(postURI);
        request.addHeader("Authorization", String.format("JWT %s", token));
        Long start = System.currentTimeMillis();
        try (CloseableHttpClient client = HttpClientBuilder.create()
                .setDefaultRequestConfig(requestConfig)
                .build();
             CloseableHttpResponse response = client
                     .execute(request)) {
            String res = IOUtils.toString(response.getEntity().getContent(), StandardCharsets.UTF_8);
            LOGGER.info("Response statu from workflow: " + response.getStatusLine().getStatusCode());
            MeetoraLinkResponse meetoraLinkResponse = OBJECT_MAPPER.readValue(res, MeetoraLinkResponse.class);
            if (response.getStatusLine().getStatusCode() == 200) {
                return OBJECT_MAPPER.readValue(res, MeetoraLinkResponse.class);
            } else {
                throw new RuntimeException(meetoraLinkResponse.getError());
            }
        } finally {
            LOGGER.info("Time taken to get response from workflow: " + (System.currentTimeMillis() - start));
        }
    }

}
