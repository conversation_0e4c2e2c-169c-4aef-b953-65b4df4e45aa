<#import "template.ftl" as layout>

<@layout.registrationLayout displayMessage=!messagesPerField.existsError('otp') ; section>

    <#if section = "header">
        ${msg("loginOtpTitle")}

    <#elseif section = "form">
        <div id="kc-form">
            <div id="kc-form-wrapper">
                <form id="kc-otp-form" action="${url.loginAction}" method="post">

                    <div class="${properties.kcFormGroupClass!}">
                        <label for="otp" class="${properties.kcLabelClass!}">${msg("loginOtpOneTime")}</label>
                        <input type="text"
                               id="otp"
                               name="otp"
                               autocomplete="off"
                               class="${properties.kcInputClass!} textfield"
                               required
                               autofocus
                               aria-invalid="<#if messagesPerField.existsError('otp')>true</#if>" />
                        <#if messagesPerField.existsError('otp')>
                            <span id="input-error" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                ${kcSanitize(messagesPerField.getFirstError('otp'))?no_esc}
                            </span>
                        </#if>
                    </div>

                    <div id="kc-form-buttons" class="${properties.kcFormGroupClass!}" style="display:flex; flex-direction:column; align-items:center;">
                        <input class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!} login-btn"
                               name="login"
                               id="kc-login"
                               type="submit"
                               value='${msg("doSubmit")}'
                               style="background-color:#1B2346; width:50%; color:white; height: 40px; border-radius: 20px; border: 1px solid #1B2346;" />
                    </div>
                </form>
            </div>
        </div>

    </#if>

</@layout.registrationLayout>