<#import "template.ftl" as layout>
<@layout.registrationLayout displayMessage=!messagesPerField.existsError('username','password') displayInfo=realm.password && realm.registrationAllowed && !registrationDisabled??; section>

    <#if section="header">
        ${msg("loginAccountTitle")}
    <#elseif section="form">
        <div id="kc-form">
            <div id="kc-form-wrapper">
                <#if realm.password>
                    <form id="kc-form-login" onsubmit="login.disabled = true; return true;" action="${url.loginAction}" method="post">
                        <div class="${properties.kcFormGroupClass!}">
                            <label hidden for="username" class="${properties.kcLabelClass!}">
                                <#if !realm.loginWithEmailAllowed>
                                    ${msg("username")}
                                <#elseif !realm.registrationEmailAsUsername>
                                    ${msg("usernameOrEmail")}
                                <#else>
                                    ${msg("email")}
                                </#if>
                            </label>
                            <#if usernameEditDisabled??>
                                <input tabindex="1" id="username" class="${properties.kcInputClass!} textfield" name="username" value="${(login.username!'')}" type="text" disabled />
                            <#else>
                                <input tabindex="1" id="username" class="${properties.kcInputClass!} textfield" name="username" value="${(login.username!'')}" type="text" autofocus autocomplete="off"
                                    aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>" />
                                <#if messagesPerField.existsError('username','password')>
                                    <span id="input-error" class="${properties.kcInputErrorMessageClass!}" aria-live="polite">
                                        ${kcSanitize(messagesPerField.getFirstError('username','password'))?no_esc}
                                    </span>
                                </#if>
                            </#if>
                        </div>
                        <div class="${properties.kcFormGroupClass!}">
                            <label hidden for="password" class="${properties.kcLabelClass!}">
                                ${msg("password")}
                            </label>
                            <input tabindex="2" id="password" class="${properties.kcInputClass!} textfield" name="password" type="password" autocomplete="off"
                                aria-invalid="<#if messagesPerField.existsError('username','password')>true</#if>" />
                        </div>
                        <div class="${properties.kcFormGroupClass!} ${properties.kcFormSettingClass!}">
                            <div id="kc-form-options">
                                <#if realm.rememberMe && !usernameEditDisabled??>
                                    <div class="checkbox">
                                        <label>
                                            <#if login.rememberMe??>
                                                <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox" checked>
                                                ${msg("rememberMe")}
                                            <#else>
                                                <input tabindex="3" id="rememberMe" name="rememberMe" type="checkbox">
                                                ${msg("rememberMe")}
                                            </#if>
                                        </label>
                                    </div>
                                </#if>
                            </div>
                            <div class="${properties.kcFormOptionsWrapperClass!}">
                                <#if realm.resetPasswordAllowed>
                                    <span><a tabindex="5" href="${url.loginResetCredentialsUrl}"> ${msg("doForgotPassword")} </a></span>
                                </#if>
                            </div>
                        </div>
                        <div id="kc-form-buttons" class="${properties.kcFormGroupClass!}" style="display:flex; flex-direction:column; align-items:center; ">
                            <#if auth.selectedCredential?has_content>
                                <input type="hidden" id="id-hidden-input" name="credentialId"  value="${auth.selectedCredential}"> />
                            <#else>
                                <input type="hidden" id="id-hidden-input" name="credentialId"/>
                            </#if> 
                            <input tabindex="4" class="${properties.kcButtonClass!} ${properties.kcButtonPrimaryClass!} ${properties.kcButtonBlockClass!} ${properties.kcButtonLargeClass!} login-btn" name="login" id="kc-login" type="submit" value='${msg("doLogIn")}' style="background-color:#1B2346; width:50%; color:white; height: 40px; border-radius: 20px; border: 1px solid #1B2346;" />
                        </div>
                    </form>
                </#if>
            </div>
            <#if realm.password && social.providers??>
                <div id="kc-social-providers" class="${properties.kcFormSocialAccountSectionClass!}">
                    <hr />
                    <h4> ${msg("identity-provider-login-label")} </h4>
                    <ul class="${properties.kcFormSocialAccountListClass!} <#if social.providers?size gt 3> ${properties.kcFormSocialAccountListGridClass!} </#if>">
                        <#list social.providers as p>
                            <a id="social-${p.alias}" style="background-color:#1B2346; width:50%; color:white; height: 40px; border-radius: 20px; border: 1px solid #1B2346;" class="${properties.kcFormSocialAccountListButtonClass!} <#if social.providers?size gt 3> ${properties.kcFormSocialAccountGridItem!} </#if>" type="button" href="${p.loginUrl}">
                                <#if p.iconClasses?has_content>
                                    <i class="${properties.kcCommonLogoIdP!} ${p.iconClasses!}" aria-hidden="true"></i>
                                    <span class="${properties.kcFormSocialAccountNameClass!} kc-social-icon-text">
                                        ${p.displayName!}
                                    </span>
                                <#else>
                                    <span class="${properties.kcFormSocialAccountNameClass!}">
                                        ${p.displayName!}
                                    </span>
                                </#if>
                            </a>
                        </#list>
                    </ul>
                </div>
            </#if>
        </div>
    <#elseif section="info">
        <#if realm.password && realm.registrationAllowed && !registrationDisabled??>
            <div id="kc-registration-container">
                <div id="kc-registration">
                    <span> ${msg("noAccount")} <a tabindex="6" href="${url.registrationUrl}"> ${msg("doRegister")} </a></span>
                </div>
            </div>
        </#if>
    </#if>

</@layout.registrationLayout>
