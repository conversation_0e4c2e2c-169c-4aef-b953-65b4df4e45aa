:root {
  --color-primary: #1b2346;
  --color-primary-fg: white;
  --color-secondary: #62b356;
  --color-input-bg: #eef2f6;
  --color-input-fg: #5b7798;
}

#kc-page-title {
  font-size: 1em;
}
.kc-logo-text * {
  display: none;
  margin: 0;
  height: 110px;
  width: 195px;
  max-width: 25vw;
  margin: 32px;
}
.textfield.textfield {
  padding: 5px 50px;
  border-radius: 31px;
  border: 1px solid var(--color-input-bg);
  background-color: var(--color-input-bg) !important;
  background-position: 20px;
  color: var(--color-input-fg);
  height: 55px;
  width: 425px;

  box-shadow: none;
  position: relative;
  opacity: 1;
  border-radius: 31px;
  margin-bottom: 16px;
}
.textfield.textfield[name="username"]:before {
  content: " ";
  position: absolute;
  top: 2px;
  left: 2px;
  background: url(../img/username.png);
  opacity: 1;
}
div.kc-logo-text {
  background: transparent url(../img/sehatuk-logo.png) 0% 0% no-repeat
    padding-box;
  opacity: 1;
  background-size: 100%;
  max-width: 20vw;
  height: 105px;
  width: 195px;
}

.login-pf-page {
  display: flex;
  width: 100vw;
  height: 100vh;
  flex-direction: column;
  align-items: flex-start;
}

.login-pf-page-header {
  flex: 1 1 auto;
  background: white !important;
  width: 100%;
  display: flex;
  height: auto;
  display: none;
}

.card-pf {
  flex: 0 0 none;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: transparent;
}

.page-end-bg {
  display: flex;
  flex: 1 1 auto;
  width: 100%;
  content: " ";
  background: white;
  background-size: cover;
}
.page-end-bg img {
  max-width: 100vw;
}
.login-pf body {
  background: white !important;
}

.login-btn {
  background: var(--color-primary) !important;
  color: var(--color-primary-fg);
  height: 55px;
  border-radius: 30px;
}

.sehatuk-header-text {
  text-transform: lowercase;
  letter-spacing: 0px;
  text-align: start;
}

.sehatuk-header-text.sehatuk-header-text_primary {
  color: var(--color-primary);
  font-size: xx-large;
  font-weight: bold;
  margin-top: 30px;
  margin: 0;
  margin-top: 16px;
}
.sehatuk-header-text.sehatuk-header-text_secondary {
  color: var(--color-secondary);
  font-size: x-large;
  font-weight: 500;
  margin: 0;
}

#kc-header {
  padding: 32px 0px;
  display: none;
}
#kc-form-buttons {
  margin-top: 0px;
}
@media only screen and (max-width: 670px) {
  #kc-header-wrapper {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    display: none;
  }
  div.kc-logo-text {
    height: 50px;
  }
  .sehatuk-header-text_primary.sehatuk-header-text_primary {
    font-size: 1.2rem;
  }
  .sehatuk-header-text_secondary.sehatuk-header-text_secondary {
    font-size: 1.2rem;
  }
  .sehatuk-header-text {
    text-align: center;
  }
  #kc-header,
  #kc-header-wrapper {
    /* padding: 0 20px !important; */
    padding: 0 8px;
    width: 90vw;
    display: none;
  }

  .page-end-bg img {
    max-width: 100vw;
    display: none;
  }
  .textfield.textfield {
    width: 220px;
  }

  .card-pf {
    transform: unset;
    left: unset;
    height: unset;
    position: unset;
    flex: 1 1 auto;
    /* max-width: 80vw; */
    width: 90vw;
    margin: 0 auto;
  }
  .page-end-bg {
    background: white url(../img/bg-login.png) center center no-repeat;
    background-size: cover;
  }
}
