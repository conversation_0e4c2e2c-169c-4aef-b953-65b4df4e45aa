parent=base
import=common/keycloak

styles=node_modules/patternfly/dist/css/patternfly.min.css node_modules/patternfly/dist/css/patternfly-additions.min.css css/account.css

##### css classes for form buttons
# main class used for all buttons
kcButtonClass=btn
# classes defining priority of the button - primary or default (there is typically only one priority button for the form)
kcButtonPrimaryClass=btn-primary
kcButtonDefaultClass=btn-default
# classes defining size of the button
kcButtonLargeClass=btn-lg
