name: <PERSON>uild and Push Docker Images

on:
  push:
    tags:
      - sehatuk.release.**
jobs:
  build_keycloak:
    name: Build Keycloak images
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Get Git Tag
        id: tag
        run: echo ::set-output name=tag::${GITHUB_REF#refs/*/}

      - name: Login to OCIR (iohealthmain)
        uses: docker/login-action@v2
        with:
          registry: dxb.ocir.io
          username: ${{ secrets.NEW_DOCKER_REGISTRY_USERNAME }}
          password: ${{ secrets.NEW_DOCKER_REGISTRY_PASSWORD }}

      - name: Build and push Docker images (iohealthmain)
        uses: docker/build-push-action@v6
        with:
          context: .
          file: ./Dockerfile
          tags: dxb.ocir.io/axpebais8u12/keycloak:${{ steps.tag.outputs.tag }}
          cache-from: type=registry,ref=dxb.ocir.io/axpebais8u12/keycloak:build-cache,oci-mediatypes=true
          cache-to: type=registry,ref=dxb.ocir.io/axpebais8u12/keycloak:build-cache,mode=max,oci-mediatypes=true,image-manifest=true
          push: true

#TODO: uncomment this once it's ready for merge

#  build-kubernetes:
#    name: Build Kubernetes
#    needs: build_keycloak
#    runs-on: ubuntu-latest
#    permissions:
#      contents: read
#      packages: write
#
#    steps:
#      - uses: actions/checkout@v3
#
#      - uses: benjlevesque/short-sha@v3.0
#        id: short-sha
#        with:
#          length: 6
#
#      - name: Check out code
#        uses: actions/checkout@v3
#        with:
#          ref: iohealth-main
#          token: ${{ secrets.GIT_TOKEN }}
#          repository: my-workforce/Kubernetes
#
#      - name: Setup Kustomize
#        uses: imranismail/setup-kustomize@v1
#        with:
#          kustomize-version: "3.6.1"
#
#      - name: Update Kubernetes resources
#        run: |
#          cd overlays/io/test/keycloak
#          kustomize edit set image keycloak=dxb.ocir.io/axpebais8u12/keycloak:21.1.2-${{ steps.short-sha.outputs.sha }}
#          cat kustomization.yaml
#
#      - name: Commit files
#        run: |
#          git config --local user.email "<EMAIL>"
#          git config --local user.name "Ajadaa"
#          git commit -am "Bump keycloak test docker tag"
#
#      - name: Push changes
#        uses: ad-m/github-push-action@master
#        with:
#          repository: my-workforce/Kubernetes
#          github_token: ${{ secrets.GIT_TOKEN }}
#          branch: iohealth-main
